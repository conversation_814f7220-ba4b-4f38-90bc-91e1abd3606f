# =============================================================================
# R包安装脚本 - RDA冗余分析所需包
# =============================================================================

cat("=== 开始安装RDA分析所需的R包 ===\n")

# 设置CRAN镜像（使用清华大学镜像，速度较快）
options(repos = c(CRAN = "https://mirrors.tuna.tsinghua.edu.cn/CRAN/"))

# 必需的包列表
required_packages <- c(
  "vegan",      # 生态学分析包，包含RDA功能
  "readxl",     # 读取Excel文件
  "dplyr",      # 数据处理
  "ggplot2",    # 绘图
  "gridExtra",  # 图形排列
  "RColorBrewer", # 颜色方案
  "corrplot",   # 相关性图
  "car",        # 回归分析
  "openxlsx"    # 写入Excel文件
)

# 可选的包（如果安装失败不影响主要功能）
optional_packages <- c(
  "VennDiagram"  # 韦恩图（可选）
)

# 安装必需包
cat("\n--- 安装必需包 ---\n")
for(pkg in required_packages) {
  cat("检查包:", pkg, "... ")
  
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("需要安装\n")
    tryCatch({
      install.packages(pkg, dependencies = TRUE)
      library(pkg, character.only = TRUE)
      cat("✅ 安装成功:", pkg, "\n")
    }, error = function(e) {
      cat("❌ 安装失败:", pkg, "-", e$message, "\n")
    })
  } else {
    cat("✅ 已存在\n")
  }
}

# 安装可选包
cat("\n--- 安装可选包 ---\n")
for(pkg in optional_packages) {
  cat("检查包:", pkg, "... ")
  
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("需要安装\n")
    tryCatch({
      install.packages(pkg, dependencies = TRUE)
      library(pkg, character.only = TRUE)
      cat("✅ 安装成功:", pkg, "\n")
    }, error = function(e) {
      cat("⚠️ 安装失败（可选包）:", pkg, "-", e$message, "\n")
    })
  } else {
    cat("✅ 已存在\n")
  }
}

# 验证核心包
cat("\n--- 验证核心包 ---\n")
core_packages <- c("vegan", "readxl", "dplyr")
all_core_available <- TRUE

for(pkg in core_packages) {
  if(require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("✅", pkg, "可用\n")
  } else {
    cat("❌", pkg, "不可用\n")
    all_core_available <- FALSE
  }
}

if(all_core_available) {
  cat("\n🎉 所有核心包安装成功！可以运行RDA分析脚本了。\n")
} else {
  cat("\n⚠️ 部分核心包安装失败，请手动安装或检查网络连接。\n")
  cat("手动安装命令:\n")
  for(pkg in core_packages) {
    if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
      cat("install.packages('", pkg, "')\n", sep = "")
    }
  }
}

cat("\n=== 包安装脚本执行完毕 ===\n")
