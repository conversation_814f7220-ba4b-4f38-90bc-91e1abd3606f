# =============================================================================
# 结果导出和报告生成函数（简化版）
# =============================================================================

# 导出RDA分析结果（简化版）
export_rda_results <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n💾 === 导出分析结果 ===\n")
  
  # 检查输入
  if(is.null(rda_results)) {
    cat("⚠️ RDA结果为空，跳过导出\n")
    return(NULL)
  }
  
  # 导出Excel报告和文本摘要
  tryCatch({

    # 创建Excel工作簿
    wb <- createWorkbook()

    # 创建文本报告
    report_file <- file.path(output_dir, "RDA分析摘要.txt")
    excel_file <- file.path(output_dir, "RDA分析结果.xlsx")
    
    if(analysis_type == "stratified") {
      # 分层结果摘要
      report_lines <- c(
        "=== RDA分层分析摘要 ===",
        paste("分析时间:", Sys.time()),
        ""
      )
      
      for(layer_name in names(rda_results)) {
        result <- rda_results[[layer_name]]
        if(!is.null(result)) {
          report_lines <- c(report_lines,
            paste("深度层:", layer_name),
            paste("  样本数:", result$sample_size),
            paste("  解释度:", round(result$explained_variance, 2), "%"),
            paste("  显著性P值:", round(result$overall_test$`Pr(>F)`[1], 4)),
            ""
          )
        }
      }
    } else {
      # 整体结果摘要
      report_lines <- c(
        "=== RDA整体分析摘要 ===",
        paste("分析时间:", Sys.time()),
        "",
        paste("样本数:", rda_results$sample_size),
        paste("解释度:", round(rda_results$explained_variance, 2), "%"),
        paste("显著性P值:", round(rda_results$overall_test$`Pr(>F)`[1], 4))
      )
    }
    
    writeLines(report_lines, report_file)
    cat("✅ 分析摘要已保存:", basename(report_file), "\n")

    # 创建Excel报告
    if(analysis_type == "stratified") {
      # 分层结果Excel
      summary_data <- data.frame(
        深度层 = names(rda_results),
        样本数 = sapply(rda_results, function(x) if(!is.null(x)) x$sample_size else 0),
        解释度 = sapply(rda_results, function(x) if(!is.null(x)) round(x$explained_variance, 2) else 0),
        显著性P值 = sapply(rda_results, function(x) if(!is.null(x)) round(x$overall_test$`Pr(>F)`[1], 4) else NA),
        stringsAsFactors = FALSE
      )
    } else {
      # 整体结果Excel
      summary_data <- data.frame(
        项目 = c("样本数", "解释度(%)", "显著性P值", "RDA1解释度(%)", "RDA2解释度(%)"),
        值 = c(
          rda_results$sample_size,
          round(rda_results$explained_variance, 2),
          round(rda_results$overall_test$`Pr(>F)`[1], 4),
          if(length(rda_results$axis_variance) >= 1) round(rda_results$axis_variance[1], 2) else 0,
          if(length(rda_results$axis_variance) >= 2) round(rda_results$axis_variance[2], 2) else 0
        ),
        stringsAsFactors = FALSE
      )
    }

    # 添加工作表
    addWorksheet(wb, "RDA分析结果")
    writeData(wb, "RDA分析结果", summary_data)

    # 保存Excel文件
    saveWorkbook(wb, excel_file, overwrite = TRUE)
    cat("✅ Excel报告已保存:", basename(excel_file), "\n")
    
  }, error = function(e) {
    cat("⚠️ 导出过程中出现错误:", e$message, "\n")
    cat("跳过详细导出，继续分析...\n")
  })
  
  cat("✅ 结果导出完成\n")
}
