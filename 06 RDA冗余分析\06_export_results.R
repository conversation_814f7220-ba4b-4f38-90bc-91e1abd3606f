# =============================================================================
# 结果导出和报告生成函数
# =============================================================================

# 导出RDA分析结果
export_rda_results <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n💾 === 导出分析结果 ===\n")
  
  # 创建Excel工作簿
  wb <- createWorkbook()
  
  if(analysis_type == "stratified") {
    # 分层结果导出
    
    # 1. 汇总结果表
    summary_data <- data.frame()
    
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next
      
      # 整体显著性
      overall_p <- result$overall_test$`Pr(>F)`[1]
      
      # 各轴显著性
      axis_p_values <- result$axis_test$`Pr(>F)`
      significant_axes <- sum(axis_p_values < 0.05, na.rm = TRUE)
      
      # 变量显著性
      terms_p_values <- result$terms_test$`Pr(>F)`
      significant_terms <- sum(terms_p_values < 0.05, na.rm = TRUE)
      
      summary_row <- data.frame(
        深度层 = layer_name,
        样本数 = result$sample_size,
        变量数 = result$variable_count,
        总解释度_百分比 = round(result$explained_variance, 2),
        整体显著性_P值 = round(overall_p, 4),
        显著轴数 = significant_axes,
        总轴数 = length(axis_p_values),
        显著变量数 = significant_terms,
        总变量数 = length(terms_p_values),
        RDA1解释度 = ifelse(length(result$axis_variance) >= 1, round(result$axis_variance[1], 2), 0),
        RDA2解释度 = ifelse(length(result$axis_variance) >= 2, round(result$axis_variance[2], 2), 0)
      )
      
      summary_data <- rbind(summary_data, summary_row)
    }
    
    # 添加汇总工作表
    addWorksheet(wb, "分层RDA汇总")
    writeData(wb, "分层RDA汇总", summary_data)
    
    # 2. 各深度层详细结果
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next
      
      sheet_name <- paste0(layer_name, "_详细")
      addWorksheet(wb, sheet_name)
      
      # 基本信息
      basic_info <- data.frame(
        项目 = c("深度层", "样本数", "变量数", "总解释度(%)", "整体显著性P值", "约束轴数", "非约束轴数"),
        值 = c(layer_name, 
               result$sample_size,
               result$variable_count,
               round(result$explained_variance, 2),
               round(result$overall_test$`Pr(>F)`[1], 4),
               length(result$rda_model$CCA$eig),
               length(result$rda_model$CA$eig))
      )
      writeData(wb, sheet_name, basic_info, startRow = 1)
      
      # 各轴解释度
      if(length(result$axis_variance) > 0) {
        axis_info <- data.frame(
          RDA轴 = names(result$axis_variance),
          解释度_百分比 = round(as.numeric(result$axis_variance), 2)
        )
        writeData(wb, sheet_name, "各轴解释度:", startRow = 10)
        writeData(wb, sheet_name, axis_info, startRow = 11)
      }
      
      # 变量显著性检验
      if(!is.null(result$terms_test)) {
        terms_result <- result$terms_test
        terms_result$变量名 <- rownames(terms_result)
        terms_result$显著性 <- ifelse(terms_result$`Pr(>F)` < 0.001, "***",
                                   ifelse(terms_result$`Pr(>F)` < 0.01, "**",
                                         ifelse(terms_result$`Pr(>F)` < 0.05, "*", "ns")))
        
        writeData(wb, sheet_name, "变量显著性检验:", startRow = 16)
        writeData(wb, sheet_name, terms_result, startRow = 17)
      }
    }
    
  } else {
    # 整体结果导出
    result <- rda_results
    if(!is.null(result)) {
      
      # 1. 汇总结果
      overall_p <- result$overall_test$`Pr(>F)`[1]
      axis_p_values <- result$axis_test$`Pr(>F)`
      significant_axes <- sum(axis_p_values < 0.05, na.rm = TRUE)
      terms_p_values <- result$terms_test$`Pr(>F)`
      significant_terms <- sum(terms_p_values < 0.05, na.rm = TRUE)
      
      summary_data <- data.frame(
        分析类型 = "整体数据",
        样本数 = result$sample_size,
        变量数 = result$variable_count,
        总解释度_百分比 = round(result$explained_variance, 2),
        整体显著性_P值 = round(overall_p, 4),
        显著轴数 = significant_axes,
        总轴数 = length(axis_p_values),
        显著变量数 = significant_terms,
        总变量数 = length(terms_p_values)
      )
      
      addWorksheet(wb, "整体RDA汇总")
      writeData(wb, "整体RDA汇总", summary_data)
      
      # 2. 详细结果
      addWorksheet(wb, "详细结果")
      
      # 基本信息
      basic_info <- data.frame(
        项目 = c("分析类型", "样本数", "变量数", "总解释度(%)", "整体显著性P值", "约束轴数", "非约束轴数"),
        值 = c("整体数据",
               result$sample_size,
               result$variable_count,
               round(result$explained_variance, 2),
               round(overall_p, 4),
               length(result$rda_model$CCA$eig),
               length(result$rda_model$CA$eig))
      )
      writeData(wb, "详细结果", basic_info, startRow = 1)
      
      # 各轴解释度
      if(length(result$axis_variance) > 0) {
        axis_info <- data.frame(
          RDA轴 = names(result$axis_variance),
          解释度_百分比 = round(as.numeric(result$axis_variance), 2)
        )
        writeData(wb, "详细结果", "各轴解释度:", startRow = 10)
        writeData(wb, "详细结果", axis_info, startRow = 11)
      }
      
      # 变量显著性检验
      if(!is.null(result$terms_test)) {
        terms_result <- result$terms_test
        terms_result$变量名 <- rownames(terms_result)
        terms_result$显著性 <- ifelse(terms_result$`Pr(>F)` < 0.001, "***",
                                   ifelse(terms_result$`Pr(>F)` < 0.01, "**",
                                         ifelse(terms_result$`Pr(>F)` < 0.05, "*", "ns")))
        
        writeData(wb, "详细结果", "变量显著性检验:", startRow = 16)
        writeData(wb, "详细结果", terms_result, startRow = 17)
      }
    }
  }
  
  # 3. 数据概况
  addWorksheet(wb, "数据概况")
  
  data_summary <- data.frame(
    项目 = c("总样本数", "响应变量数", "环境变量数", "缺失值阈值", "置换检验次数"),
    值 = c(nrow(processed_data$data),
           length(processed_data$response_vars),
           length(processed_data$env_vars),
           paste0(missing_threshold * 100, "%"),
           permutation_tests)
  )
  writeData(wb, "数据概况", data_summary, startRow = 1)
  
  # 响应变量列表
  response_info <- data.frame(
    序号 = 1:length(processed_data$response_vars),
    响应变量 = processed_data$response_vars
  )
  writeData(wb, "数据概况", "响应变量列表:", startRow = 8)
  writeData(wb, "数据概况", response_info, startRow = 9)
  
  # 环境变量列表
  env_info <- data.frame(
    序号 = 1:length(processed_data$env_vars),
    环境变量 = processed_data$env_vars
  )
  writeData(wb, "数据概况", "环境变量列表:", startRow = 15)
  writeData(wb, "数据概况", env_info, startRow = 16)
  
  # 保存Excel文件
  excel_filename <- ifelse(analysis_type == "stratified", 
                          "分层RDA详细结果.xlsx", 
                          "整体RDA详细结果.xlsx")
  saveWorkbook(wb, file.path(output_dir, excel_filename), overwrite = TRUE)
  cat("✅ Excel结果文件已保存:", excel_filename, "\n")
}

# 生成分析报告
generate_analysis_report <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n📋 === 生成分析报告 ===\n")
  
  report_file <- file.path(output_dir, "RDA分析报告.txt")
  
  # 创建报告内容
  report_content <- c(
    "=============================================================================",
    "东北黑土区土壤剖面RDA冗余分析报告",
    "=============================================================================",
    "",
    paste("分析时间:", Sys.time()),
    paste("分析类型:", ifelse(analysis_type == "stratified", "分层RDA分析", "整体RDA分析")),
    "",
    "--- 数据概况 ---",
    paste("总样本数:", nrow(processed_data$data)),
    paste("响应变量数:", length(processed_data$response_vars)),
    paste("环境变量数:", length(processed_data$env_vars)),
    paste("缺失值阈值:", missing_threshold * 100, "%"),
    paste("置换检验次数:", permutation_tests),
    "",
    "响应变量:",
    paste("  ", processed_data$response_vars, collapse = "\n"),
    ""
  )
  
  if(analysis_type == "stratified") {
    report_content <- c(report_content,
      "--- 分层RDA分析结果 ---",
      ""
    )
    
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next
      
      report_content <- c(report_content,
        paste("深度层:", layer_name),
        paste("  样本数:", result$sample_size),
        paste("  变量数:", result$variable_count),
        paste("  总解释度:", round(result$explained_variance, 2), "%"),
        paste("  整体显著性P值:", round(result$overall_test$`Pr(>F)`[1], 4)),
        paste("  约束轴数:", length(result$rda_model$CCA$eig)),
        ""
      )
    }
  } else {
    if(!is.null(rda_results)) {
      report_content <- c(report_content,
        "--- 整体RDA分析结果 ---",
        paste("样本数:", rda_results$sample_size),
        paste("变量数:", rda_results$variable_count),
        paste("总解释度:", round(rda_results$explained_variance, 2), "%"),
        paste("整体显著性P值:", round(rda_results$overall_test$`Pr(>F)`[1], 4)),
        paste("约束轴数:", length(rda_results$rda_model$CCA$eig)),
        ""
      )
    }
  }
  
  report_content <- c(report_content,
    "--- 输出文件列表 ---",
    "图表文件:",
    "  - RDA双序图 (PNG格式)",
    "  - 变量重要性图 (PNG格式)",
    "  - 解释度对比图 (PNG格式)",
    "  - 土壤属性相关性热图 (PNG格式)",
    "  - 环境变量分组分布图 (PNG格式)",
    "  - 土壤属性变化雷达图 (PNG格式)",
    "",
    "数据文件:",
    "  - RDA详细结果 (XLSX格式)",
    "  - 分析报告 (TXT格式)",
    "",
    "=============================================================================",
    "报告生成完成"
  )
  
  # 写入报告文件
  writeLines(report_content, report_file, useBytes = TRUE)
  cat("✅ 分析报告已保存:", "RDA分析报告.txt", "\n")
}
