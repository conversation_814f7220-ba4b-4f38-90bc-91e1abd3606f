# =============================================================================
# 结果导出和报告生成函数（简化版）
# =============================================================================

# 导出RDA分析结果（简化版）
export_rda_results <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n💾 === 导出分析结果 ===\n")
  
  # 检查输入
  if(is.null(rda_results)) {
    cat("⚠️ RDA结果为空，跳过导出\n")
    return(NULL)
  }
  
  # 导出Excel报告和文本摘要
  tryCatch({

    # 创建Excel工作簿
    wb <- createWorkbook()

    # 创建文本报告
    report_file <- file.path(output_dir, "RDA分析摘要.txt")
    excel_file <- file.path(output_dir, "RDA分析结果.xlsx")
    
    if(analysis_type == "stratified") {
      # 分层结果摘要
      report_lines <- c(
        "=== RDA分层分析摘要 ===",
        paste("分析时间:", Sys.time()),
        ""
      )
      
      for(layer_name in names(rda_results)) {
        result <- rda_results[[layer_name]]
        if(!is.null(result)) {
          report_lines <- c(report_lines,
            paste("深度层:", layer_name),
            paste("  样本数:", result$sample_size),
            paste("  解释度:", round(result$explained_variance, 2), "%"),
            paste("  显著性P值:", round(result$overall_test$`Pr(>F)`[1], 4)),
            ""
          )
        }
      }
    } else {
      # 整体结果摘要
      report_lines <- c(
        "=== RDA整体分析摘要 ===",
        paste("分析时间:", Sys.time()),
        "",
        paste("样本数:", rda_results$sample_size),
        paste("解释度:", round(rda_results$explained_variance, 2), "%"),
        paste("显著性P值:", round(rda_results$overall_test$`Pr(>F)`[1], 4))
      )
    }
    
    writeLines(report_lines, report_file)
    cat("✅ 分析摘要已保存:", basename(report_file), "\n")

    # 创建详细的Excel报告（论文级）
    if(analysis_type == "stratified") {
      # 分层结果的详细报告
      create_stratified_excel_report(wb, rda_results)
    } else {
      # 整体结果的详细报告
      create_comprehensive_excel_report(wb, rda_results)
    }

    # 保存Excel文件
    saveWorkbook(wb, excel_file, overwrite = TRUE)
    cat("✅ 详细Excel报告已保存:", basename(excel_file), "\n")
    
  }, error = function(e) {
    cat("⚠️ 导出过程中出现错误:", e$message, "\n")
    cat("跳过详细导出，继续分析...\n")
  })
  
  cat("✅ 结果导出完成\n")
}

# 创建详细的Excel报告（论文级）
create_comprehensive_excel_report <- function(wb, rda_result) {

  # 1. 基本统计摘要
  basic_stats <- data.frame(
    项目 = c("分析类型", "样本数量", "响应变量数", "环境变量数",
             "总解释度(%)", "调整R²", "显著性P值", "F统计量"),
    值 = c("冗余分析(RDA)",
           rda_result$sample_size,
           ncol(scores(rda_result$rda_model, display = "species")),
           rda_result$variable_count,
           round(rda_result$explained_variance, 3),
           round(RsquareAdj(rda_result$rda_model)$adj.r.squared, 3),
           round(rda_result$overall_test$`Pr(>F)`[1], 4),
           round(rda_result$overall_test$F[1], 3)),
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "1-基本统计")
  writeData(wb, "1-基本统计", basic_stats)

  # 2. 轴解释度详细信息
  eigenvals <- eigenvals(rda_result$rda_model)
  total_var <- sum(eigenvals)

  axis_details <- data.frame(
    排序轴 = paste0("RDA", 1:length(eigenvals)),
    特征值 = round(eigenvals, 4),
    解释度_百分比 = round(eigenvals / total_var * 100, 3),
    累积解释度 = round(cumsum(eigenvals) / total_var * 100, 3),
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "2-轴解释度")
  writeData(wb, "2-轴解释度", axis_details)

  # 3. 环境变量重要性分析
  if(!is.null(rda_result$explanatory_data)) {
    tryCatch({
      env_fit <- envfit(rda_result$rda_model, rda_result$explanatory_data, permutations = 999)

      var_importance <- data.frame(
        环境变量 = names(env_fit$vectors$r),
        R平方 = round(env_fit$vectors$r, 4),
        P值 = round(env_fit$vectors$pvals, 4),
        显著性 = ifelse(env_fit$vectors$pvals < 0.001, "***",
                      ifelse(env_fit$vectors$pvals < 0.01, "**",
                             ifelse(env_fit$vectors$pvals < 0.05, "*", "ns"))),
        重要性等级 = ifelse(env_fit$vectors$r > 0.3, "高",
                         ifelse(env_fit$vectors$r > 0.1, "中", "低")),
        stringsAsFactors = FALSE
      )

      # 按重要性排序
      var_importance <- var_importance[order(var_importance$R平方, decreasing = TRUE), ]

      addWorksheet(wb, "3-变量重要性")
      writeData(wb, "3-变量重要性", var_importance)

    }, error = function(e) {
      cat("⚠️ 环境变量重要性分析失败\n")
    })
  }

  # 4. 样本得分
  site_scores <- scores(rda_result$rda_model, display = "sites", choices = 1:min(4, ncol(scores(rda_result$rda_model, display = "sites"))))
  site_scores_df <- data.frame(
    样本ID = rownames(site_scores),
    site_scores,
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "4-样本得分")
  writeData(wb, "4-样本得分", site_scores_df)

  # 5. 响应变量得分
  species_scores <- scores(rda_result$rda_model, display = "species", choices = 1:min(4, ncol(scores(rda_result$rda_model, display = "species"))))
  species_scores_df <- data.frame(
    响应变量 = rownames(species_scores),
    species_scores,
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "5-响应变量得分")
  writeData(wb, "5-响应变量得分", species_scores_df)

  # 6. 环境变量得分
  env_scores <- scores(rda_result$rda_model, display = "bp", choices = 1:min(4, ncol(scores(rda_result$rda_model, display = "bp"))))
  env_scores_df <- data.frame(
    环境变量 = rownames(env_scores),
    env_scores,
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "6-环境变量得分")
  writeData(wb, "6-环境变量得分", env_scores_df)

  cat("✅ 已创建6个详细工作表\n")
}

# 创建分层分析的Excel报告
create_stratified_excel_report <- function(wb, rda_results) {

  # 1. 分层结果汇总
  summary_data <- data.frame(
    深度层 = names(rda_results),
    样本数 = sapply(rda_results, function(x) if(!is.null(x)) x$sample_size else 0),
    解释度_百分比 = sapply(rda_results, function(x) if(!is.null(x)) round(x$explained_variance, 3) else 0),
    调整R平方 = sapply(rda_results, function(x) if(!is.null(x)) round(RsquareAdj(x$rda_model)$adj.r.squared, 3) else 0),
    显著性P值 = sapply(rda_results, function(x) if(!is.null(x)) round(x$overall_test$`Pr(>F)`[1], 4) else NA),
    F统计量 = sapply(rda_results, function(x) if(!is.null(x)) round(x$overall_test$F[1], 3) else NA),
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, "1-分层结果汇总")
  writeData(wb, "1-分层结果汇总", summary_data)

  # 2. 为每个深度层创建详细工作表
  for(layer_name in names(rda_results)) {
    if(!is.null(rda_results[[layer_name]])) {
      create_layer_detail_sheet(wb, rda_results[[layer_name]], layer_name)
    }
  }

  cat("✅ 已创建分层分析详细报告\n")
}

# 为单个深度层创建详细工作表
create_layer_detail_sheet <- function(wb, rda_result, layer_name) {

  sheet_name <- paste0("详细-", layer_name)

  # 基本统计
  basic_stats <- data.frame(
    项目 = c("深度层", "样本数", "解释度(%)", "调整R²", "P值"),
    值 = c(layer_name,
           rda_result$sample_size,
           round(rda_result$explained_variance, 3),
           round(RsquareAdj(rda_result$rda_model)$adj.r.squared, 3),
           round(rda_result$overall_test$`Pr(>F)`[1], 4)),
    stringsAsFactors = FALSE
  )

  addWorksheet(wb, sheet_name)
  writeData(wb, sheet_name, basic_stats, startRow = 1)

  # 轴解释度
  eigenvals <- eigenvals(rda_result$rda_model)
  total_var <- sum(eigenvals)

  axis_details <- data.frame(
    排序轴 = paste0("RDA", 1:length(eigenvals)),
    解释度_百分比 = round(eigenvals / total_var * 100, 3),
    stringsAsFactors = FALSE
  )

  writeData(wb, sheet_name, data.frame(项目 = "轴解释度", 值 = ""), startRow = nrow(basic_stats) + 3)
  writeData(wb, sheet_name, axis_details, startRow = nrow(basic_stats) + 4)
}
