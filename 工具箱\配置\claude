claude-an --dangerously-skip-permissions
/terminal-setup
/test MyButton

mcp服务
claude mcp add context7 cmd /c npx @upstash/context7-mcp -s user
claude mcp add browser cmd /c npx @agent-infra/mcp-server-browser@latest -s user
claude mcp add sequential-thinking cmd /c npx @modelcontextprotocol/server-sequential-thinking -s user
claude mcp add filesystem cmd /c npx @modelcontextprotocol/server-filesystem C:/ E:/ -s user
claude mcp add sequential-thinking cmd /c npx @modelcontextprotocol/server-sequential-thinking -s user
claude mcp add  sequential-thinking -- cmd /c npx @modelcontextprotocol/server-sequential-thinking
