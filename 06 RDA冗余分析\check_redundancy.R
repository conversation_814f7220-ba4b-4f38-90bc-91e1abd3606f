# 检查冗余变量的原因
cat("=== 检查转移变量冗余的原因 ===\n")

# 加载数据
source("06 RDA冗余分析/02_load_packages.R")
source("06 RDA冗余分析/03_data_processing.R")

vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
response_vars <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")

vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
change_data <- read_excel(change_file_path)

processed_result <- preprocess_data_with_transitions(vif_data, change_data, response_vars, create_transitions = TRUE)

cat("\n=== 土壤类型转移分布 ===\n")
soiltype_1980 <- processed_result$data$`Soilclass-1980`
soiltype_2023 <- processed_result$data$`Soilclass-2023`
soiltype_1980[is.na(soiltype_1980)] <- "未知"
soiltype_2023[is.na(soiltype_2023)] <- "未知"
soiltype_transition <- paste(soiltype_1980, "→", soiltype_2023, sep = "")

# 统计每种转移类型的样本数
transition_counts <- table(soiltype_transition)
cat("各转移类型的样本数:\n")
print(sort(transition_counts, decreasing = TRUE))

cat("\n=== 被标记为冗余的转移类型 ===\n")
redundant_types <- c('草甸土→黑土', '草甸土→沼泽土', '黑土→暗棕壤', '黑土→黑土', 
                     '沼泽土→草甸土', '沼泽土→黑土', '沼泽土→沼泽土')

for(type in redundant_types) {
  count <- sum(soiltype_transition == type)
  cat(type, ":", count, "个样本\n")
}

cat("\n=== 土地利用转移分布 ===\n")
landuse_1980 <- processed_result$data$`LandUse-1980`
landuse_2023 <- processed_result$data$`LandUse-2023`
landuse_1980[is.na(landuse_1980)] <- "未知"
landuse_2023[is.na(landuse_2023)] <- "未知"
landuse_transition <- paste(landuse_1980, "→", landuse_2023, sep = "")

landuse_counts <- table(landuse_transition)
cat("各转移类型的样本数:\n")
print(sort(landuse_counts, decreasing = TRUE))

cat("\n=== 分析冗余原因 ===\n")
cat("可能的原因:\n")
cat("1. 样本数太少（<3个样本）的转移类型\n")
cat("2. 与其他变量完全共线性\n")
cat("3. 在当前数据集中为常量（所有样本都相同）\n")

# 检查转移变量的虚拟变量编码
cat("\n=== 检查虚拟变量编码 ===\n")
soiltype_factor <- factor(soiltype_transition)
dummy_matrix <- model.matrix(~ soiltype_factor - 1)
cat("土壤转移虚拟变量矩阵维度:", dim(dummy_matrix), "\n")
cat("列名:\n")
print(colnames(dummy_matrix))

# 检查哪些列是常量或接近常量
constant_cols <- apply(dummy_matrix, 2, function(x) length(unique(x)) <= 1)
cat("\n常量列:\n")
print(colnames(dummy_matrix)[constant_cols])

# 检查低方差列
low_var_cols <- apply(dummy_matrix, 2, function(x) var(x) < 0.01)
cat("\n低方差列（方差<0.01）:\n")
print(colnames(dummy_matrix)[low_var_cols])
