import pandas as pd
import numpy as np
import os
import warnings
from openpyxl import load_workbook
from openpyxl.styles import Alignment

# --- 1. 全局配置 ---
# ----------------------------------------------------------------------
# 请在此处配置您的输入、输出文件路径和列名
# ----------------------------------------------------------------------

# 输入Excel文件的完整路径
input_path = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\模型预测结果\嫩江_完整数据.xlsx"

# 输出统计结果Excel文件的完整路径
# 默认在输入文件同目录下创建一个名为 "统计结果_最终版.xlsx" 的文件
output_path =  r"E:\05 Python\Devway\01 硕士论文\00 原始数据\统计结果.xlsx"

# 需要进行描述性统计的数值列名列表
analysis_cols = [
    'pH', 'SOM', 'TN', 'TP', '物理粘粒'
]

# 用于按"县城"分组的列名
county_col = 'City'

# 用于按"深度范围"分组的列名
depth_col = '深度范围'

# 用于按"年份"分组的列名
year_col = 'year'

# --- 2. 核心函数 ---
# ----------------------------------------------------------------------
# 此区域包含核心的统计计算与格式化函数
# ----------------------------------------------------------------------

def suppress_warnings():
    """抑制在统计计算中可能出现的常见运行时警告。"""
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='Mean of empty slice')
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='Degrees of freedom <= 0 for slice')

def get_overall_stats(df, columns):
    """
    计算给定数据表的整体描述性统计量。
    - 对每列独立处理缺失值。
    - 只计算在 'columns' 中指定的列。
    """
    stats_list = []
    for col in columns:
        if col not in df.columns:
            continue  # 如果列不存在，则跳过
            
        # 仅对数值型数据进行统计
        if pd.api.types.is_numeric_dtype(df[col]):
            data = df[col].dropna()
            count = len(data)
            if count > 0:
                mean_val = data.mean()
                std_val = data.std()
                cv_val = (std_val / mean_val * 100) if mean_val != 0 and pd.notna(mean_val) else np.nan
                stats = {
                    '列名': col,
                    '样本数': count,
                    '最小值': data.min(),
                    '最大值': data.max(),
                    '平均值': mean_val,
                    '中位数': data.median(),
                    '标准差': std_val,
                    '变异系数(%)': cv_val
                }
            else:
                stats = {'列名': col, '样本数': 0}
            stats_list.append(stats)
            
    return pd.DataFrame(stats_list)

def get_grouped_stats(df, group_by, columns):
    """
    按指定列分组，计算描述性统计量。
    此版本只生成纯数据，不进行任何会改变数据的"美化"操作。
    """
    required_cols = group_by + columns
    if not all(col in df.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in df.columns]
        print(f"  [警告] 表格中缺少以下必需列: {missing_cols}")
        return pd.DataFrame()

    summary_list = []
    for group_keys, group_data in df.groupby(group_by, dropna=False, sort=True):
        if isinstance(group_keys, tuple):
            group_dict = dict(zip(group_by, group_keys))
        else:
            group_dict = {group_by[0]: group_keys}

        for col in columns:
            if col in group_data.columns and pd.api.types.is_numeric_dtype(group_data[col]):
                data = group_data[col].dropna()
                count = len(data)
                if count > 0:
                    mean_val, std_val = data.mean(), data.std()
                    cv_val = (std_val / mean_val * 100) if mean_val != 0 and pd.notna(mean_val) else np.nan
                    stats = {
                        '列名': col, '样本数': count, '最小值': data.min(), '最大值': data.max(),
                        '平均值': mean_val, '中位数': data.median(), '标准差': std_val, '变异系数(%)': cv_val
                    }
                else:
                    stats = {'列名': col, '样本数': 0}
                summary_list.append({**group_dict, **stats})

    if not summary_list:
        return pd.DataFrame()

    summary_df = pd.DataFrame(summary_list)
    # 确保最终输出前排序，为后续格式化做准备
    summary_df = summary_df.sort_values(by=group_by).reset_index(drop=True)

    front_cols = group_by + ['列名', '样本数']
    other_cols = [col for col in summary_df.columns if col not in front_cols]
    return summary_df[front_cols + other_cols]

def apply_excel_formatting(workbook_path, sheets_format_info):
    """
    打开一个已保存的Excel文件，并对指定的工作表和列应用单元格合并与居中格式。

    Args:
        workbook_path (str): Excel文件路径。
        sheets_format_info (dict): 格式化信息，键为工作表名，值为需要合并的列名列表。
    """
    print("\n--- 应用Excel格式化 ---")
    try:
        workbook = load_workbook(workbook_path)
    except FileNotFoundError:
        print(f"[错误] 格式化失败，文件未找到: {workbook_path}")
        return

    alignment = Alignment(horizontal='center', vertical='center')

    for sheet_name, columns_to_merge in sheets_format_info.items():
        if sheet_name not in workbook.sheetnames:
            print(f"  [警告] 未找到工作表 '{sheet_name}'，跳过。")
            continue
        
        sheet = workbook[sheet_name]
        print(f"  > 正在格式化工作表: '{sheet_name}'")

        header = [cell.value for cell in sheet[1]]
        for col_name in columns_to_merge:
            try:
                col_idx = header.index(col_name) + 1
            except ValueError:
                continue

            start_row = 2
            for row in range(2, sheet.max_row + 2):
                current_value = sheet.cell(row=row, column=col_idx).value
                start_value = sheet.cell(row=start_row, column=col_idx).value
                
                if current_value != start_value:
                    if start_row < row - 1:
                        sheet.merge_cells(start_row=start_row, start_column=col_idx, end_row=row - 1, end_column=col_idx)
                        sheet.cell(row=start_row, column=col_idx).alignment = alignment
                    start_row = row
    
    print("--- 格式化完成，正在保存 ---")
    workbook.save(workbook_path)

# --- 3. 主处理流程 ---
# ----------------------------------------------------------------------
# 主流程，负责读取、处理和写入Excel文件
# ----------------------------------------------------------------------

def process_workbook():
    """
    主函数：生成所有统计数据，写入Excel，然后应用格式化。
    现在支持同一sheet中包含year列的数据格式。
    """
    suppress_warnings()
    if not os.path.exists(input_path):
        print(f"[错误] 输入文件不存在: {input_path}")
        return

    print(f"正在读取: {input_path}")
    try:
        excel_file = pd.ExcelFile(input_path)
    except Exception as e:
        print(f"[错误] 无法读取Excel文件: {e}")
        return

    sheets_to_format = {}

    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        print(f"准备将数据写入: {output_path}")
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- 处理工作表: '{sheet_name}' ---")
            try:
                df = excel_file.parse(sheet_name)
            except Exception as e:
                print(f"  [错误] 无法解析 '{sheet_name}'，已跳过: {e}")
                continue

            # 检查是否包含year列
            if year_col not in df.columns:
                print(f"  [警告] 工作表 '{sheet_name}' 中未找到年份列 '{year_col}'，跳过处理")
                continue

            # 获取所有年份
            years = sorted(df[year_col].unique())
            print(f"  发现年份: {years}")

            sn_abbr = sheet_name[:15]

            # 处理每个年份的数据
            for year in years:
                year_df = df[df[year_col] == year].copy()
                year_suffix = f"{year}"
                print(f"\n  --- 处理 {year} 年数据 ---")

                # 1. 整体统计
                print("    1. 计算整体统计...")
                overall_stats_df = get_overall_stats(year_df, analysis_cols)
                if not overall_stats_df.empty:
                    overall_stats_df.to_excel(writer, sheet_name=f"{sn_abbr}_{year_suffix}_整体", index=False)
                    print(f"       -> 已生成: {sn_abbr}_{year_suffix}_整体")

                # 2. 深度统计
                print(f"    2. 按 '{depth_col}' 分组...")
                depth_stats_df = get_grouped_stats(year_df, [depth_col], analysis_cols)
                if not depth_stats_df.empty:
                    sheet_name_depth = f"{sn_abbr}_{year_suffix}_深度"
                    depth_stats_df.to_excel(writer, sheet_name=sheet_name_depth, index=False)
                    sheets_to_format[sheet_name_depth] = [depth_col]
                    print(f"       -> 已生成: {sheet_name_depth}")

                # 3. 县城统计
                print(f"    3. 按 '{county_col}' 分组...")
                county_stats_df = get_grouped_stats(year_df, [county_col], analysis_cols)
                if not county_stats_df.empty:
                    sheet_name_county = f"{sn_abbr}_{year_suffix}_县城"
                    county_stats_df.to_excel(writer, sheet_name=sheet_name_county, index=False)
                    sheets_to_format[sheet_name_county] = [county_col]
                    print(f"       -> 已生成: {sheet_name_county}")

                # 4. 县城与深度组合统计
                print(f"    4. 按 '{county_col}' 和 '{depth_col}' 组合...")
                combo_stats_df = get_grouped_stats(year_df, [county_col, depth_col], analysis_cols)
                if not combo_stats_df.empty:
                    sheet_name_combo = f"{sn_abbr}_{year_suffix}_县城深度"
                    combo_stats_df.to_excel(writer, sheet_name=sheet_name_combo, index=False)
                    sheets_to_format[sheet_name_combo] = [county_col, depth_col]
                    print(f"       -> 已生成: {sheet_name_combo}")

            # 5. 跨年份对比统计
            print(f"\n  --- 生成跨年份对比统计 ---")

            # 5.1 按年份统计
            print(f"    5.1 按 '{year_col}' 分组...")
            year_stats_df = get_grouped_stats(df, [year_col], analysis_cols)
            if not year_stats_df.empty:
                sheet_name_year = f"{sn_abbr}_年份对比"
                year_stats_df.to_excel(writer, sheet_name=sheet_name_year, index=False)
                sheets_to_format[sheet_name_year] = [year_col]
                print(f"       -> 已生成: {sheet_name_year}")

            # 5.2 按年份和县城统计
            print(f"    5.2 按 '{year_col}' 和 '{county_col}' 组合...")
            year_county_stats_df = get_grouped_stats(df, [year_col, county_col], analysis_cols)
            if not year_county_stats_df.empty:
                sheet_name_year_county = f"{sn_abbr}_年份县城"
                year_county_stats_df.to_excel(writer, sheet_name=sheet_name_year_county, index=False)
                sheets_to_format[sheet_name_year_county] = [year_col, county_col]
                print(f"       -> 已生成: {sheet_name_year_county}")

            # 5.3 按年份和深度统计
            print(f"    5.3 按 '{year_col}' 和 '{depth_col}' 组合...")
            year_depth_stats_df = get_grouped_stats(df, [year_col, depth_col], analysis_cols)
            if not year_depth_stats_df.empty:
                sheet_name_year_depth = f"{sn_abbr}_年份深度"
                year_depth_stats_df.to_excel(writer, sheet_name=sheet_name_year_depth, index=False)
                sheets_to_format[sheet_name_year_depth] = [year_col, depth_col]
                print(f"       -> 已生成: {sheet_name_year_depth}")

            # 5.4 按年份、县城和深度统计
            print(f"    5.4 按 '{year_col}', '{county_col}' 和 '{depth_col}' 组合...")
            full_combo_stats_df = get_grouped_stats(df, [year_col, county_col, depth_col], analysis_cols)
            if not full_combo_stats_df.empty:
                sheet_name_full_combo = f"{sn_abbr}_完整组合"
                full_combo_stats_df.to_excel(writer, sheet_name=sheet_name_full_combo, index=False)
                sheets_to_format[sheet_name_full_combo] = [year_col, county_col, depth_col]
                print(f"       -> 已生成: {sheet_name_full_combo}")

    print(f"\n--- 数据写入完成 ---")
    
    if sheets_to_format:
        apply_excel_formatting(output_path, sheets_to_format)

    print(f"所有结果已成功保存并格式化至: {output_path}")

# --- 4. 脚本执行 ---
# ----------------------------------------------------------------------
# 程序执行的入口
# ----------------------------------------------------------------------

if __name__ == "__main__":
    process_workbook() 