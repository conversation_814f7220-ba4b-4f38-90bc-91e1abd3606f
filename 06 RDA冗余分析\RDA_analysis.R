# =============================================================================
# 东北黑土区土壤剖面RDA冗余分析脚本
# 
# 功能：
# 1. 土地利用变化处理（1980->2023转移变量）
# 2. 土壤类型变化处理（1980->2023转移变量）  
# 3. 县域归属处理（控制空间异质性）
# 4. 分层RDA模型（0-10、10-30、30-60、60-100）
# 5. 配置开关（县域协变量、分层RDA）
# =============================================================================

# 清理环境
rm(list = ls())
gc()

# 加载必要的包
cat("=== 加载R包 ===\n")

# 核心必需包
core_packages <- c("vegan", "readxl", "dplyr")
# 可选包（用于增强功能）
optional_packages <- c("ggplot2", "gridExtra", "RColorBrewer", "corrplot", "car", "openxlsx")

# 检查核心包
missing_core <- c()
for(pkg in core_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    missing_core <- c(missing_core, pkg)
    cat("❌ 缺少核心包:", pkg, "\n")
  } else {
    cat("✅ 核心包已加载:", pkg, "\n")
  }
}

if(length(missing_core) > 0) {
  cat("\n⚠️ 缺少核心包，请先运行 install_packages.R 安装所需包\n")
  cat("或手动安装:", paste(missing_core, collapse = ", "), "\n")
  stop("缺少必需的R包，无法继续执行")
}

# 检查可选包
available_optional <- c()
for(pkg in optional_packages) {
  if(require(pkg, character.only = TRUE, quietly = TRUE)) {
    available_optional <- c(available_optional, pkg)
    cat("✅ 可选包已加载:", pkg, "\n")
  } else {
    cat("⚠️ 可选包未安装:", pkg, "（部分功能可能受限）\n")
  }
}

cat("核心包加载完成，可以进行RDA分析\n\n")

# =============================================================================
# 配置参数区域
# =============================================================================

# --- 文件路径配置 ---
vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
output_dir <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/RDA冗余分析"

# 检查文件是否存在
if(!file.exists(vif_file_path)) {
  stop("VIF筛选结果文件不存在: ", vif_file_path)
}
if(!file.exists(change_file_path)) {
  stop("变化量数据文件不存在: ", change_file_path)
}

# --- 分析配置开关 ---
# 是否将县域作为协变量/随机效应（TRUE: 作为协变量, FALSE: 不使用）
use_county_covariate <- TRUE

# 县域列名（指定数据中县域信息的列名）
county_column_name <- "City"

# 是否进行分层RDA（TRUE: 按深度分层, FALSE: 不分层对所有数据进行RDA）
use_stratified_rda <- TRUE

# 缺失值处理配置
# 缺失值阈值：当某行缺失值比例超过此阈值时，删除整行（建议0.3，即30%）
missing_threshold <- 0.3

# 数值变量缺失值填补方式（"mean": 均值, "median": 中位数）
numeric_missing_method <- "mean"

# 分类变量缺失值填补方式（"mode": 众数, "remove": 删除）
categorical_missing_method <- "mode"

# RDA显著性检验参数
permutation_tests <- 999  # 置换检验次数

# --- 深度分层定义 ---
depth_layers <- list(
  "0-10cm" = c(0, 10),
  "10-30cm" = c(10, 30), 
  "30-60cm" = c(30, 60),
  "60-100cm" = c(60, 100)
)

# =============================================================================
# 辅助函数定义
# =============================================================================

# 创建输出目录
create_output_dir <- function(dir_path) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE)
    cat("创建输出目录:", dir_path, "\n")
  } else {
    cat("输出目录已存在:", dir_path, "\n")
  }
}

# 配置验证函数
validate_config <- function() {
  cat("=== 配置验证 ===\n")

  # 验证文件路径
  if(!file.exists(vif_file_path)) {
    stop("❌ VIF筛选结果文件不存在: ", vif_file_path)
  } else {
    cat("✓ VIF筛选结果文件存在\n")
  }

  if(!file.exists(change_file_path)) {
    stop("❌ 变化量数据文件不存在: ", change_file_path)
  } else {
    cat("✓ 变化量数据文件存在\n")
  }

  # 验证配置参数
  if(missing_threshold < 0 || missing_threshold > 1) {
    stop("❌ 缺失值阈值必须在0-1之间")
  } else {
    cat("✓ 缺失值阈值设置合理:", missing_threshold, "\n")
  }

  if(permutation_tests < 99) {
    warning("⚠️ 置换检验次数较少，建议至少999次")
  } else {
    cat("✓ 置换检验次数:", permutation_tests, "\n")
  }

  cat("✓ 配置验证通过\n\n")
}

# 众数函数
get_mode <- function(x) {
  x <- x[!is.na(x)]
  if(length(x) == 0) return(NA)
  ux <- unique(x)
  ux[which.max(tabulate(match(x, ux)))]
}

# 改进的缺失值处理函数
handle_missing_values <- function(data, threshold = 0.3,
                                 numeric_method = "mean",
                                 categorical_method = "mode") {
  cat("=== 缺失值处理 ===\n")
  cat("缺失值阈值:", threshold * 100, "%\n")
  cat("数值变量填补方法:", numeric_method, "\n")
  cat("分类变量填补方法:", categorical_method, "\n")

  original_rows <- nrow(data)

  # 1. 计算每行的缺失值比例
  missing_per_row <- rowSums(is.na(data)) / ncol(data)
  rows_to_remove <- missing_per_row > threshold

  if(sum(rows_to_remove) > 0) {
    cat("删除", sum(rows_to_remove), "行缺失值比例超过阈值的数据\n")
    data <- data[!rows_to_remove, ]
  }

  # 2. 分析剩余数据的缺失值情况
  missing_summary <- colSums(is.na(data))
  missing_cols <- missing_summary[missing_summary > 0]

  if(length(missing_cols) > 0) {
    cat("剩余缺失值情况:\n")
    for(col_name in names(missing_cols)) {
      cat("  ", col_name, ":", missing_cols[col_name], "个缺失值\n")
    }
  } else {
    cat("没有剩余缺失值\n")
    return(data)
  }

  # 3. 分别处理数值型和分类型变量
  numeric_cols <- sapply(data, is.numeric)
  categorical_cols <- !numeric_cols

  # 处理数值型变量
  if(any(numeric_cols)) {
    numeric_data <- data[, numeric_cols, drop = FALSE]
    missing_numeric <- colSums(is.na(numeric_data))
    missing_numeric <- missing_numeric[missing_numeric > 0]

    if(length(missing_numeric) > 0) {
      cat("填补数值型变量缺失值:\n")
      for(col_name in names(missing_numeric)) {
        if(numeric_method == "mean") {
          fill_value <- mean(numeric_data[[col_name]], na.rm = TRUE)
        } else if(numeric_method == "median") {
          fill_value <- median(numeric_data[[col_name]], na.rm = TRUE)
        } else {
          fill_value <- get_mode(numeric_data[[col_name]])
        }

        data[[col_name]][is.na(data[[col_name]])] <- fill_value
        cat("  ", col_name, ": 用", round(fill_value, 4), "填补", missing_numeric[col_name], "个缺失值\n")
      }
    }
  }

  # 处理分类型变量
  if(any(categorical_cols)) {
    categorical_data <- data[, categorical_cols, drop = FALSE]
    missing_categorical <- colSums(is.na(categorical_data))
    missing_categorical <- missing_categorical[missing_categorical > 0]

    if(length(missing_categorical) > 0) {
      cat("填补分类型变量缺失值:\n")
      for(col_name in names(missing_categorical)) {
        if(categorical_method == "mode") {
          fill_value <- get_mode(categorical_data[[col_name]])
          data[[col_name]][is.na(data[[col_name]])] <- fill_value
          cat("  ", col_name, ": 用'", fill_value, "'填补", missing_categorical[col_name], "个缺失值\n")
        } else if(categorical_method == "remove") {
          # 删除该列的缺失值行
          rows_with_na <- is.na(data[[col_name]])
          data <- data[!rows_with_na, ]
          cat("  ", col_name, ": 删除", sum(rows_with_na), "行含缺失值的数据\n")
        }
      }
    }
  }

  final_rows <- nrow(data)
  cat("数据行数变化:", original_rows, "->", final_rows, "(删除", original_rows - final_rows, "行)\n")

  return(data)
}

# 创建土地利用转移变量
create_landuse_transition_vars <- function(data) {
  cat("创建土地利用转移变量...\n")
  
  # 获取土地利用类型
  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`
  
  # 创建转移变量名称
  unique_1980 <- unique(landuse_1980[!is.na(landuse_1980)])
  unique_2023 <- unique(landuse_2023[!is.na(landuse_2023)])
  
  cat("1980年土地利用类型:", paste(unique_1980, collapse = ", "), "\n")
  cat("2023年土地利用类型:", paste(unique_2023, collapse = ", "), "\n")
  
  # 创建转移变量
  transition_vars <- data.frame(row.names = rownames(data))
  
  for(from_type in unique_1980) {
    for(to_type in unique_2023) {
      if(!is.na(from_type) && !is.na(to_type)) {
        var_name <- paste0("LU_", gsub("[^A-Za-z0-9]", "", from_type), "_to_", gsub("[^A-Za-z0-9]", "", to_type))
        transition_vars[[var_name]] <- as.numeric(
          landuse_1980 == from_type & landuse_2023 == to_type
        )
        transition_vars[[var_name]][is.na(transition_vars[[var_name]])] <- 0
      }
    }
  }
  
  cat("创建了", ncol(transition_vars), "个土地利用转移变量\n")
  return(transition_vars)
}

# 创建土壤类型转移变量
create_soiltype_transition_vars <- function(data) {
  cat("创建土壤类型转移变量...\n")
  
  # 获取土壤类型
  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`
  
  # 获取唯一类型
  unique_1980 <- unique(soiltype_1980)
  unique_2023 <- unique(soiltype_2023)
  
  cat("1980年土壤类型:", paste(unique_1980, collapse = ", "), "\n")
  cat("2023年土壤类型:", paste(unique_2023, collapse = ", "), "\n")
  
  # 创建转移变量
  transition_vars <- data.frame(row.names = rownames(data))
  
  for(from_type in unique_1980) {
    for(to_type in unique_2023) {
      var_name <- paste0("ST_", gsub("[^A-Za-z0-9]", "", from_type), "_to_", gsub("[^A-Za-z0-9]", "", to_type))
      transition_vars[[var_name]] <- as.numeric(
        soiltype_1980 == from_type & soiltype_2023 == to_type
      )
    }
  }
  
  cat("创建了", ncol(transition_vars), "个土壤类型转移变量\n")
  return(transition_vars)
}

# 数据预处理函数
preprocess_data <- function(vif_data, change_data) {
  cat("开始数据预处理...\n")

  # 合并VIF筛选结果和完整变化量数据
  # 使用VIF筛选后的环境变量，但保留完整的基础信息
  base_cols <- c("ProfileID", "Longitude", "Latitude", "City", "Location",
                 "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
                 "深度范围", "深度中点")

  # 响应变量（土壤属性变化量）
  response_cols <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")

  # VIF筛选后的环境变量（排除基础信息和响应变量）
  env_cols <- setdiff(colnames(vif_data), c(base_cols, response_cols))

  # 构建最终数据集
  final_cols <- c(base_cols, response_cols, env_cols)
  available_cols <- intersect(final_cols, colnames(change_data))

  processed_data <- change_data[, available_cols]

  cat("数据维度:", nrow(processed_data), "行,", ncol(processed_data), "列\n")
  cat("响应变量:", paste(response_cols, collapse = ", "), "\n")
  cat("环境变量数量:", length(env_cols), "\n")

  return(list(
    data = processed_data,
    response_vars = response_cols,
    env_vars = env_cols,
    base_vars = base_cols
  ))
}

# 基于VIF分析脚本的完整变量分类函数
classify_variables <- function(env_vars) {
  cat("=== 变量分类（基于VIF分析脚本定义）===\n")

  # 从VIF分析脚本中复制的完整变量分组定义
  variable_groups <- list(
    climate = c(
      # 气候相关变量
      'Air_Temperature_2m_Mean', 'LST', 'Total_Precipitation_Mean', 'Total_Precipitation_Sum',
      'Total_Evaporation_Mean', 'Surface_Net_Solar_Radiation_Mean', 'Surface_Net_Thermal_Radiation_Mean',
      'Wind_U_Component_10m_Mean', 'Wind_V_Component_10m_Mean'
    ),
    terrain = c(
      # 地形相关变量
      '250DEM', 'Aspect', 'Slope', 'Plan_Curvature', 'Profile_Curvature',
      'Terrain_Ruggedness_Index', 'Topographic_Position_Index', 'Topographic_Wetness_Index',
      'Valley_Depth', 'Vertical_Distance_to_Channel_Network', 'Flow_Accumulation',
      'Flow_Direction', 'Sink_Route'
    ),
    vegetation = c(
      # 植被相关变量
      'NDVI', 'EVI', 'NDWI', 'MNDWI', 'SAVI', 'RVI', 'LAI', 'FAPAR', 'FVC',
      'NPP', 'GPP', 'BSI', 'IBI', 'Coloration_Index', 'Redness_Index', 'Saturation_Index'
    ),
    human = c(
      # 人类活动变量
      'Population', 'GDP', 'NTL', 'Built_Up', 'CLCD'
    ),
    soil_3d = c(
      # 3D土壤变量（不聚合）
      'bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'
    ),
    categorical = c(
      # 分类变量
      'landform', 'lithology', 'soiltype'
    )
  )

  # 静态协变量：地形 + 分类变量（相对稳定的因子）
  static_vars <- c(variable_groups$terrain, variable_groups$categorical)

  # 动态协变量：气候 + 植被 + 人类活动 + 3D土壤（随时间变化的因子）
  dynamic_vars <- c(variable_groups$climate, variable_groups$vegetation,
                   variable_groups$human, variable_groups$soil_3d)

  # 筛选实际存在的变量
  static_available <- intersect(static_vars, env_vars)
  dynamic_available <- intersect(dynamic_vars, env_vars)

  # 未分类的变量
  unclassified <- setdiff(env_vars, c(static_available, dynamic_available))

  cat("静态协变量(", length(static_available), "个):\n")
  cat("  地形变量:", paste(intersect(variable_groups$terrain, static_available), collapse = ", "), "\n")
  cat("  分类变量:", paste(intersect(variable_groups$categorical, static_available), collapse = ", "), "\n")

  cat("动态协变量(", length(dynamic_available), "个):\n")
  cat("  气候变量:", paste(intersect(variable_groups$climate, dynamic_available), collapse = ", "), "\n")
  cat("  植被变量:", paste(intersect(variable_groups$vegetation, dynamic_available), collapse = ", "), "\n")
  cat("  人类活动:", paste(intersect(variable_groups$human, dynamic_available), collapse = ", "), "\n")
  cat("  3D土壤:", paste(intersect(variable_groups$soil_3d, dynamic_available), collapse = ", "), "\n")

  if(length(unclassified) > 0) {
    cat("未分类变量(", length(unclassified), "个):", paste(unclassified, collapse = ", "), "\n")
    cat("将未分类变量归入动态协变量\n")
    dynamic_available <- c(dynamic_available, unclassified)
  }

  return(list(
    static = static_available,
    dynamic = dynamic_available,
    groups = variable_groups,
    classification_summary = list(
      static_terrain = intersect(variable_groups$terrain, static_available),
      static_categorical = intersect(variable_groups$categorical, static_available),
      dynamic_climate = intersect(variable_groups$climate, dynamic_available),
      dynamic_vegetation = intersect(variable_groups$vegetation, dynamic_available),
      dynamic_human = intersect(variable_groups$human, dynamic_available),
      dynamic_soil3d = intersect(variable_groups$soil_3d, dynamic_available),
      unclassified = unclassified
    )
  ))
}

# RDA分析函数
perform_rda_analysis <- function(response_data, explanatory_data, condition_data = NULL,
                                layer_name = "全部数据", permutations = 999) {
  cat("\n=== 执行RDA分析:", layer_name, "===\n")

  # 检查数据完整性
  if(nrow(response_data) != nrow(explanatory_data)) {
    stop("响应变量和解释变量的行数不匹配")
  }

  # 移除常量列
  constant_cols <- sapply(explanatory_data, function(x) length(unique(x[!is.na(x)])) <= 1)
  if(any(constant_cols)) {
    cat("移除常量列:", paste(names(explanatory_data)[constant_cols], collapse = ", "), "\n")
    explanatory_data <- explanatory_data[, !constant_cols, drop = FALSE]
  }

  if(ncol(explanatory_data) == 0) {
    cat("警告: 没有有效的解释变量\n")
    return(NULL)
  }

  # 执行RDA
  if(is.null(condition_data) || ncol(condition_data) == 0) {
    # 无条件RDA
    rda_result <- rda(response_data ~ ., data = explanatory_data)
    cat("执行无条件RDA\n")
  } else {
    # 条件RDA（偏RDA）
    rda_result <- rda(response_data, explanatory_data, condition_data)
    cat("执行条件RDA（偏RDA），条件变量数:", ncol(condition_data), "\n")
  }

  # 显著性检验
  cat("进行显著性检验...\n")
  overall_test <- anova(rda_result, permutations = permutations)
  axis_test <- anova(rda_result, by = "axis", permutations = permutations)
  terms_test <- anova(rda_result, by = "terms", permutations = permutations)

  # 计算解释度
  total_var <- sum(rda_result$CA$eig) + sum(rda_result$CCA$eig)
  constrained_var <- sum(rda_result$CCA$eig)
  explained_variance <- constrained_var / total_var * 100

  cat("总解释度:", round(explained_variance, 2), "%\n")
  cat("约束轴数:", length(rda_result$CCA$eig), "\n")
  cat("非约束轴数:", length(rda_result$CA$eig), "\n")

  return(list(
    rda_model = rda_result,
    overall_test = overall_test,
    axis_test = axis_test,
    terms_test = terms_test,
    explained_variance = explained_variance,
    layer_name = layer_name
  ))
}

# 分层RDA分析函数
perform_stratified_rda <- function(processed_data, depth_layers, use_county = TRUE) {
  cat("\n=== 开始分层RDA分析 ===\n")

  results <- list()

  for(layer_name in names(depth_layers)) {
    depth_range <- depth_layers[[layer_name]]
    cat("\n--- 分析深度层:", layer_name, "(", depth_range[1], "-", depth_range[2], "cm) ---\n")

    # 筛选当前深度层的数据
    depth_filter <- processed_data$data$深度中点 >= depth_range[1] &
                   processed_data$data$深度中点 < depth_range[2]

    if(layer_name == "60-100cm") {
      # 对于最后一层，包含上边界
      depth_filter <- processed_data$data$深度中点 >= depth_range[1] &
                     processed_data$data$深度中点 <= depth_range[2]
    }

    layer_data <- processed_data$data[depth_filter, ]

    if(nrow(layer_data) == 0) {
      cat("警告: 深度层", layer_name, "没有数据\n")
      next
    }

    cat("当前层样本数:", nrow(layer_data), "\n")

    # 准备响应变量矩阵
    response_matrix <- layer_data[, processed_data$response_vars, drop = FALSE]
    response_matrix <- response_matrix[complete.cases(response_matrix), , drop = FALSE]

    if(nrow(response_matrix) == 0) {
      cat("警告: 深度层", layer_name, "没有完整的响应变量数据\n")
      next
    }

    # 准备解释变量
    explanatory_vars <- layer_data[rownames(response_matrix), processed_data$env_vars, drop = FALSE]

    # 创建转移变量
    landuse_transitions <- create_landuse_transition_vars(layer_data[rownames(response_matrix), ])
    soiltype_transitions <- create_soiltype_transition_vars(layer_data[rownames(response_matrix), ])

    # 变量分类
    var_classification <- classify_variables(processed_data$env_vars)

    # 分别处理静态和动态协变量
    static_explanatory <- explanatory_vars[, intersect(colnames(explanatory_vars), var_classification$static), drop = FALSE]
    dynamic_explanatory <- explanatory_vars[, intersect(colnames(explanatory_vars), var_classification$dynamic), drop = FALSE]

    # 合并解释变量：静态协变量 + 动态协变量 + 转移变量
    all_explanatory <- cbind(static_explanatory, dynamic_explanatory, landuse_transitions, soiltype_transitions)

    # 处理缺失值
    all_explanatory <- handle_missing_values(
      all_explanatory,
      threshold = missing_threshold,
      numeric_method = numeric_missing_method,
      categorical_method = categorical_missing_method
    )

    # 准备条件变量（县域）
    condition_vars <- NULL
    if(use_county && county_column_name %in% colnames(layer_data)) {
      county_data <- layer_data[rownames(response_matrix), county_column_name, drop = FALSE]
      unique_counties <- unique(county_data[[county_column_name]])

      if(length(unique_counties) > 1) {
        # 只有当有多个县时才作为条件变量
        formula_str <- paste("~", county_column_name, "- 1")
        condition_vars <- model.matrix(as.formula(formula_str), data = county_data)
        cat("使用县域作为条件变量，县数:", ncol(condition_vars), "\n")
        cat("县域包括:", paste(unique_counties, collapse = ", "), "\n")
      } else {
        cat("只有一个县(", unique_counties[1], ")，跳过县域条件变量\n")
      }
    } else if(use_county) {
      cat("警告: 县域列'", county_column_name, "'不存在，跳过县域条件变量\n")
    }

    # 执行RDA分析
    rda_result <- perform_rda_analysis(
      response_data = response_matrix,
      explanatory_data = all_explanatory,
      condition_data = condition_vars,
      layer_name = layer_name,
      permutations = permutation_tests
    )

    if(!is.null(rda_result)) {
      results[[layer_name]] <- rda_result
    }
  }

  return(results)
}

# 整体RDA分析函数
perform_overall_rda <- function(processed_data, use_county = TRUE) {
  cat("\n=== 开始整体RDA分析 ===\n")

  # 准备响应变量矩阵
  response_matrix <- processed_data$data[, processed_data$response_vars, drop = FALSE]
  response_matrix <- response_matrix[complete.cases(response_matrix), , drop = FALSE]

  cat("整体分析样本数:", nrow(response_matrix), "\n")

  # 准备解释变量
  explanatory_vars <- processed_data$data[rownames(response_matrix), processed_data$env_vars, drop = FALSE]

  # 创建转移变量
  landuse_transitions <- create_landuse_transition_vars(processed_data$data[rownames(response_matrix), ])
  soiltype_transitions <- create_soiltype_transition_vars(processed_data$data[rownames(response_matrix), ])

  # 变量分类
  var_classification <- classify_variables(processed_data$env_vars)

  # 分别处理静态和动态协变量
  static_explanatory <- explanatory_vars[, intersect(colnames(explanatory_vars), var_classification$static), drop = FALSE]
  dynamic_explanatory <- explanatory_vars[, intersect(colnames(explanatory_vars), var_classification$dynamic), drop = FALSE]

  # 合并解释变量：静态协变量 + 动态协变量 + 转移变量
  all_explanatory <- cbind(static_explanatory, dynamic_explanatory, landuse_transitions, soiltype_transitions)

  # 处理缺失值
  all_explanatory <- handle_missing_values(
    all_explanatory,
    threshold = missing_threshold,
    numeric_method = numeric_missing_method,
    categorical_method = categorical_missing_method
  )

  # 准备条件变量（县域）
  condition_vars <- NULL
  if(use_county && county_column_name %in% colnames(processed_data$data)) {
    county_data <- processed_data$data[rownames(response_matrix), county_column_name, drop = FALSE]
    unique_counties <- unique(county_data[[county_column_name]])

    if(length(unique_counties) > 1) {
      formula_str <- paste("~", county_column_name, "- 1")
      condition_vars <- model.matrix(as.formula(formula_str), data = county_data)
      cat("使用县域作为条件变量，县数:", ncol(condition_vars), "\n")
      cat("县域包括:", paste(unique_counties, collapse = ", "), "\n")
    } else {
      cat("只有一个县(", unique_counties[1], ")，跳过县域条件变量\n")
    }
  } else if(use_county) {
    cat("警告: 县域列'", county_column_name, "'不存在，跳过县域条件变量\n")
  }

  # 执行RDA分析
  rda_result <- perform_rda_analysis(
    response_data = response_matrix,
    explanatory_data = all_explanatory,
    condition_data = condition_vars,
    layer_name = "整体数据",
    permutations = permutation_tests
  )

  return(rda_result)
}

# 可视化函数
create_rda_plots <- function(rda_results, output_dir, is_stratified = TRUE) {
  cat("\n=== 创建RDA可视化图表 ===\n")

  if(is_stratified) {
    # 分层结果可视化
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next

      cat("绘制", layer_name, "的RDA图...\n")

      # RDA双序图
      png(file.path(output_dir, paste0("RDA_双序图_", layer_name, ".png")),
          width = 12, height = 10, units = "in", res = 300)

      plot(result$rda_model, type = "n", main = paste("RDA双序图 -", layer_name))

      # 添加样点
      points(result$rda_model, display = "sites", pch = 21, bg = "lightblue", cex = 1.2)

      # 添加物种（响应变量）
      text(result$rda_model, display = "species", col = "red", cex = 0.8, font = 2)

      # 添加环境变量箭头
      arrows(0, 0,
             scores(result$rda_model, display = "bp")[,1] * 0.8,
             scores(result$rda_model, display = "bp")[,2] * 0.8,
             length = 0.1, col = "blue", lwd = 2)

      # 添加环境变量标签
      text(scores(result$rda_model, display = "bp") * 0.9,
           labels = rownames(scores(result$rda_model, display = "bp")),
           col = "blue", cex = 0.7)

      # 添加解释度信息
      legend("topright",
             legend = paste("解释度:", round(result$explained_variance, 2), "%"),
             bty = "n", cex = 1.2)

      dev.off()
    }

    # 创建解释度对比图
    explained_vars <- sapply(rda_results, function(x) {
      if(is.null(x)) return(0)
      return(x$explained_variance)
    })

    png(file.path(output_dir, "分层RDA解释度对比.png"),
        width = 10, height = 6, units = "in", res = 300)

    barplot(explained_vars,
            main = "各深度层RDA解释度对比",
            ylab = "解释度 (%)",
            xlab = "深度层",
            col = rainbow(length(explained_vars)),
            ylim = c(0, max(explained_vars) * 1.2))

    # 添加数值标签
    text(x = seq_along(explained_vars),
         y = explained_vars + max(explained_vars) * 0.02,
         labels = paste0(round(explained_vars, 1), "%"),
         cex = 1.2, font = 2)

    dev.off()

  } else {
    # 整体结果可视化
    result <- rda_results
    if(is.null(result)) return()

    cat("绘制整体RDA图...\n")

    # RDA双序图
    png(file.path(output_dir, "RDA_双序图_整体.png"),
        width = 12, height = 10, units = "in", res = 300)

    plot(result$rda_model, type = "n", main = "RDA双序图 - 整体数据")

    # 添加样点
    points(result$rda_model, display = "sites", pch = 21, bg = "lightblue", cex = 1.2)

    # 添加物种（响应变量）
    text(result$rda_model, display = "species", col = "red", cex = 0.8, font = 2)

    # 添加环境变量箭头
    arrows(0, 0,
           scores(result$rda_model, display = "bp")[,1] * 0.8,
           scores(result$rda_model, display = "bp")[,2] * 0.8,
           length = 0.1, col = "blue", lwd = 2)

    # 添加环境变量标签
    text(scores(result$rda_model, display = "bp") * 0.9,
         labels = rownames(scores(result$rda_model, display = "bp")),
         col = "blue", cex = 0.7)

    # 添加解释度信息
    legend("topright",
           legend = paste("解释度:", round(result$explained_variance, 2), "%"),
           bty = "n", cex = 1.2)

    dev.off()
  }
}

# 保存结果函数
save_rda_results <- function(rda_results, output_dir, is_stratified = TRUE) {
  cat("\n=== 保存RDA分析结果 ===\n")

  # 创建结果汇总
  if(is_stratified) {
    # 分层结果汇总
    summary_data <- data.frame()

    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next

      # 整体显著性
      overall_p <- result$overall_test$`Pr(>F)`[1]

      # 各轴显著性
      axis_p_values <- result$axis_test$`Pr(>F)`
      significant_axes <- sum(axis_p_values < 0.05, na.rm = TRUE)

      # 变量显著性
      terms_p_values <- result$terms_test$`Pr(>F)`
      significant_terms <- sum(terms_p_values < 0.05, na.rm = TRUE)

      summary_row <- data.frame(
        深度层 = layer_name,
        解释度_百分比 = round(result$explained_variance, 2),
        整体显著性_P值 = round(overall_p, 4),
        显著轴数 = significant_axes,
        总轴数 = length(axis_p_values),
        显著变量数 = significant_terms,
        总变量数 = length(terms_p_values),
        样本数 = nrow(scores(result$rda_model, display = "sites"))
      )

      summary_data <- rbind(summary_data, summary_row)
    }

    # 保存汇总结果
    write.csv(summary_data, file.path(output_dir, "分层RDA结果汇总.csv"),
              row.names = FALSE, fileEncoding = "UTF-8")

    # 保存详细结果到Excel
    wb <- createWorkbook()
    addWorksheet(wb, "汇总结果")
    writeData(wb, "汇总结果", summary_data)

    # 为每个深度层保存详细的RDA结果
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next

      # 创建工作表
      sheet_name <- paste0(layer_name, "_详细结果")
      addWorksheet(wb, sheet_name)

      # 写入基本信息
      basic_info <- data.frame(
        项目 = c("深度层", "解释度(%)", "整体显著性P值", "样本数", "约束轴数", "非约束轴数"),
        值 = c(layer_name,
               round(result$explained_variance, 2),
               round(result$overall_test$`Pr(>F)`[1], 4),
               nrow(scores(result$rda_model, display = "sites")),
               length(result$rda_model$CCA$eig),
               length(result$rda_model$CA$eig))
      )
      writeData(wb, sheet_name, basic_info, startRow = 1)

      # 写入变量显著性检验结果
      if(!is.null(result$terms_test)) {
        writeData(wb, sheet_name, "变量显著性检验:", startRow = 9)
        writeData(wb, sheet_name, result$terms_test, startRow = 10)
      }
    }

    saveWorkbook(wb, file.path(output_dir, "分层RDA详细结果.xlsx"), overwrite = TRUE)

  } else {
    # 整体结果汇总
    result <- rda_results
    if(!is.null(result)) {
      overall_p <- result$overall_test$`Pr(>F)`[1]
      axis_p_values <- result$axis_test$`Pr(>F)`
      significant_axes <- sum(axis_p_values < 0.05, na.rm = TRUE)
      terms_p_values <- result$terms_test$`Pr(>F)`
      significant_terms <- sum(terms_p_values < 0.05, na.rm = TRUE)

      summary_data <- data.frame(
        分析类型 = "整体数据",
        解释度_百分比 = round(result$explained_variance, 2),
        整体显著性_P值 = round(overall_p, 4),
        显著轴数 = significant_axes,
        总轴数 = length(axis_p_values),
        显著变量数 = significant_terms,
        总变量数 = length(terms_p_values),
        样本数 = nrow(scores(result$rda_model, display = "sites"))
      )

      write.csv(summary_data, file.path(output_dir, "整体RDA结果汇总.csv"),
                row.names = FALSE, fileEncoding = "UTF-8")
    }
  }

  cat("结果已保存到:", output_dir, "\n")
}

# =============================================================================
# 主执行函数
# =============================================================================

main_analysis <- function() {
  cat("=============================================================================\n")
  cat("东北黑土区土壤剖面RDA冗余分析\n")
  cat("=============================================================================\n")

  # 配置验证
  validate_config()

  # 创建输出目录
  create_output_dir(output_dir)

  # 读取数据
  cat("\n--- 读取数据 ---\n")
  vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
  change_data <- read_excel(change_file_path)

  cat("VIF筛选数据:", nrow(vif_data), "行,", ncol(vif_data), "列\n")
  cat("变化量数据:", nrow(change_data), "行,", ncol(change_data), "列\n")

  # 数据预处理
  processed_data <- preprocess_data(vif_data, change_data)

  # 处理缺失值
  processed_data$data <- handle_missing_values(
    processed_data$data,
    threshold = missing_threshold,
    numeric_method = numeric_missing_method,
    categorical_method = categorical_missing_method
  )

  # 执行分析
  if(use_stratified_rda) {
    cat("\n📊 === 执行分层RDA分析 ===\n")
    cat("🔍 分层设置:", paste(names(depth_layers), collapse = ", "), "\n")
    cat("🏘️ 县域协变量:", ifelse(use_county_covariate, "启用", "禁用"), "\n")

    cat("\n⏳ 开始分层RDA计算...\n")
    rda_results <- perform_stratified_rda(processed_data, depth_layers, use_county_covariate)

    cat("\n🎨 生成可视化图表...\n")
    create_rda_plots(rda_results, output_dir, is_stratified = TRUE)

    cat("\n💾 保存分析结果...\n")
    save_rda_results(rda_results, output_dir, is_stratified = TRUE)

  } else {
    cat("\n📊 === 执行整体RDA分析 ===\n")
    cat("🏘️ 县域协变量:", ifelse(use_county_covariate, "启用", "禁用"), "\n")

    cat("\n⏳ 开始整体RDA计算...\n")
    rda_result <- perform_overall_rda(processed_data, use_county_covariate)

    cat("\n🎨 生成可视化图表...\n")
    create_rda_plots(rda_result, output_dir, is_stratified = FALSE)

    cat("\n💾 保存分析结果...\n")
    save_rda_results(rda_result, output_dir, is_stratified = FALSE)
  }

  cat("\n🎉 === 分析完成 ===\n")
  cat("📁 结果保存在:", output_dir, "\n")
  cat("📊 主要输出文件:\n")
  if(use_stratified_rda) {
    cat("   - 分层RDA结果汇总.csv\n")
    cat("   - 分层RDA详细结果.xlsx\n")
    cat("   - RDA_双序图_[深度层].png\n")
    cat("   - 分层RDA解释度对比.png\n")
  } else {
    cat("   - 整体RDA结果汇总.csv\n")
    cat("   - RDA_双序图_整体.png\n")
  }
  cat("\n✅ RDA冗余分析全部完成！\n")
}

# 测试函数（快速验证脚本功能）
test_analysis <- function() {
  cat("=== 快速测试模式 ===\n")

  # 临时修改配置为快速测试
  original_permutations <- permutation_tests
  permutation_tests <<- 99  # 减少置换次数以加快测试

  # 只测试一个深度层
  original_stratified <- use_stratified_rda
  use_stratified_rda <<- FALSE  # 只做整体分析

  cat("测试配置: 置换检验99次, 整体分析模式\n")

  tryCatch({
    main_analysis()
    cat("✅ 测试成功！脚本运行正常\n")
  }, error = function(e) {
    cat("❌ 测试失败:", e$message, "\n")
  }, finally = {
    # 恢复原始配置
    permutation_tests <<- original_permutations
    use_stratified_rda <<- original_stratified
  })
}

# =============================================================================
# 脚本执行入口
# =============================================================================

cat("\n=== RDA冗余分析脚本已加载 ===\n")
cat("当前配置:\n")
cat("  📊 分层RDA:", ifelse(use_stratified_rda, "启用", "禁用"), "\n")
cat("  🏘️ 县域协变量:", ifelse(use_county_covariate, "启用", "禁用"), "\n")
cat("  🔧 缺失值阈值:", missing_threshold * 100, "%\n")
cat("  🔄 置换检验次数:", permutation_tests, "\n")
cat("  📁 输出目录:", output_dir, "\n")

# 自动执行分析
cat("\n🚀 开始自动执行RDA分析...\n")
main_analysis()
