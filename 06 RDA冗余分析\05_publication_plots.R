# =============================================================================
# 论文级高质量可视化函数
# =============================================================================

# 创建高质量RDA双序图
create_rda_biplot <- function(rda_result, title = "RDA双序图", output_path) {
  
  if(is.null(rda_result) || length(rda_result$rda_model$CCA$eig) == 0) {
    cat("⚠️ RDA结果无效，跳过双序图绘制\n")
    return(NULL)
  }
  
  # 提取坐标数据
  sites_scores <- scores(rda_result$rda_model, display = "sites", choices = 1:2)
  species_scores <- scores(rda_result$rda_model, display = "species", choices = 1:2)
  env_scores <- scores(rda_result$rda_model, display = "bp", choices = 1:2)
  
  # 计算轴解释度
  axis1_var <- round(rda_result$axis_variance[1], 1)
  axis2_var <- round(rda_result$axis_variance[2], 1)
  
  # 创建数据框
  sites_df <- data.frame(sites_scores, Type = "Sites")
  species_df <- data.frame(species_scores, Type = "Species", 
                          Label = rownames(species_scores))
  env_df <- data.frame(env_scores, Type = "Environment", 
                      Label = rownames(env_scores))
  
  # 创建ggplot
  p <- ggplot() +
    # 添加样点
    geom_point(data = sites_df, aes(x = RDA1, y = RDA2), 
               color = rda_colors$primary, alpha = 0.7, size = 2) +
    
    # 添加物种（响应变量）
    geom_point(data = species_df, aes(x = RDA1, y = RDA2), 
               color = rda_colors$secondary, size = 4, shape = 17) +
    geom_text_repel(data = species_df, aes(x = RDA1, y = RDA2, label = Label),
                    color = rda_colors$secondary, size = 4, fontface = "bold",
                    box.padding = 0.5, point.padding = 0.3) +
    
    # 添加环境变量箭头
    geom_segment(data = env_df, aes(x = 0, y = 0, xend = RDA1 * 0.8, yend = RDA2 * 0.8),
                 arrow = arrow(length = unit(0.3, "cm"), type = "closed"),
                 color = rda_colors$accent, size = 1.2, alpha = 0.8) +
    geom_text_repel(data = env_df, aes(x = RDA1 * 0.9, y = RDA2 * 0.9, label = Label),
                    color = rda_colors$accent, size = 3, fontface = "bold",
                    box.padding = 0.3, point.padding = 0.2) +
    
    # 添加坐标轴
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
    geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.5) +
    
    # 设置标签和主题
    labs(
      title = title,
      subtitle = paste0("解释度: ", round(rda_result$explained_variance, 1), "%"),
      x = paste0("RDA1 (", axis1_var, "%)"),
      y = paste0("RDA2 (", axis2_var, "%)")
    ) +
    
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 14, hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12),
      panel.grid.minor = element_blank(),
      legend.position = "none"
    ) +
    
    coord_fixed(ratio = 1)
  
  # 保存图片
  ggsave(output_path, plot = p, width = 12, height = 10, dpi = 300, bg = "white")
  cat("✅ RDA双序图已保存:", output_path, "\n")
  
  return(p)
}

# 创建解释度条形图
create_variance_barplot <- function(rda_results, output_path, analysis_type = "stratified") {
  
  if(analysis_type == "stratified") {
    # 分层结果
    variance_data <- data.frame(
      Layer = names(rda_results),
      Variance = sapply(rda_results, function(x) {
        if(is.null(x)) return(0)
        return(x$explained_variance)
      }),
      stringsAsFactors = FALSE
    )
    
    # 重新排序深度层
    variance_data$Layer <- factor(variance_data$Layer, 
                                 levels = c("0-10cm", "10-30cm", "30-60cm", "60-100cm"))
    
    p <- ggplot(variance_data, aes(x = Layer, y = Variance, fill = Layer)) +
      geom_col(alpha = 0.8, width = 0.7) +
      geom_text(aes(label = paste0(round(Variance, 1), "%")), 
                vjust = -0.5, size = 5, fontface = "bold") +
      scale_fill_manual(values = rda_colors$gradient[1:4]) +
      labs(
        title = "各深度层RDA解释度对比",
        x = "深度层",
        y = "解释度 (%)"
      ) +
      theme_bw() +
      theme(
        plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
        axis.title = element_text(size = 14, face = "bold"),
        axis.text = element_text(size = 12),
        legend.position = "none",
        panel.grid.minor = element_blank()
      ) +
      ylim(0, max(variance_data$Variance) * 1.2)
    
  } else {
    # 整体结果 - 显示各轴解释度
    if(length(rda_results$axis_variance) > 0) {
      axis_data <- data.frame(
        Axis = names(rda_results$axis_variance),
        Variance = as.numeric(rda_results$axis_variance),
        stringsAsFactors = FALSE
      )
      
      p <- ggplot(axis_data, aes(x = Axis, y = Variance, fill = Axis)) +
        geom_col(alpha = 0.8, width = 0.7) +
        geom_text(aes(label = paste0(round(Variance, 1), "%")), 
                  vjust = -0.5, size = 5, fontface = "bold") +
        scale_fill_manual(values = rda_colors$gradient[1:nrow(axis_data)]) +
        labs(
          title = "RDA各轴解释度",
          x = "RDA轴",
          y = "解释度 (%)"
        ) +
        theme_bw() +
        theme(
          plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
          axis.title = element_text(size = 14, face = "bold"),
          axis.text = element_text(size = 12),
          legend.position = "none",
          panel.grid.minor = element_blank()
        ) +
        ylim(0, max(axis_data$Variance) * 1.2)
    } else {
      return(NULL)
    }
  }
  
  ggsave(output_path, plot = p, width = 10, height = 8, dpi = 300, bg = "white")
  cat("✅ 解释度条形图已保存:", output_path, "\n")
  
  return(p)
}

# 创建变量重要性图
create_variable_importance_plot <- function(rda_result, output_path, top_n = 15) {
  
  if(is.null(rda_result) || is.null(rda_result$terms_test)) {
    cat("⚠️ 无法创建变量重要性图\n")
    return(NULL)
  }
  
  # 提取变量显著性数据
  terms_data <- rda_result$terms_test
  terms_data$Variable <- rownames(terms_data)
  terms_data$Significance <- ifelse(terms_data$`Pr(>F)` < 0.001, "***",
                                   ifelse(terms_data$`Pr(>F)` < 0.01, "**",
                                         ifelse(terms_data$`Pr(>F)` < 0.05, "*", "ns")))
  
  # 按F值排序，取前top_n个
  terms_data <- terms_data[order(terms_data$F, decreasing = TRUE), ]
  if(nrow(terms_data) > top_n) {
    terms_data <- terms_data[1:top_n, ]
  }
  
  # 重新排序用于绘图
  terms_data$Variable <- factor(terms_data$Variable, levels = terms_data$Variable)
  
  p <- ggplot(terms_data, aes(x = reorder(Variable, F), y = F, fill = Significance)) +
    geom_col(alpha = 0.8) +
    geom_text(aes(label = Significance), hjust = -0.2, size = 4, fontface = "bold") +
    scale_fill_manual(values = c("***" = rda_colors$warning, 
                                "**" = rda_colors$accent,
                                "*" = rda_colors$success,
                                "ns" = rda_colors$neutral)) +
    labs(
      title = paste0("变量重要性排序 - ", rda_result$layer_name),
      x = "环境变量",
      y = "F值",
      fill = "显著性"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text.x = element_text(size = 10, angle = 45, hjust = 1),
      axis.text.y = element_text(size = 12),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 11),
      panel.grid.minor = element_blank()
    ) +
    coord_flip()
  
  ggsave(output_path, plot = p, width = 12, height = 8, dpi = 300, bg = "white")
  cat("✅ 变量重要性图已保存:", output_path, "\n")
  
  return(p)
}

# 创建响应变量相关性热图
create_response_correlation_heatmap <- function(processed_data, output_path) {
  
  # 提取响应变量数据
  response_data <- processed_data$data[, processed_data$response_vars, drop = FALSE]
  response_data <- response_data[complete.cases(response_data), ]
  
  # 计算相关性矩阵
  cor_matrix <- cor(response_data, use = "complete.obs")
  
  # 转换为长格式数据
  cor_data <- expand.grid(Var1 = rownames(cor_matrix), Var2 = colnames(cor_matrix))
  cor_data$Correlation <- as.vector(cor_matrix)
  
  # 创建热图
  p <- ggplot(cor_data, aes(x = Var1, y = Var2, fill = Correlation)) +
    geom_tile(color = "white", size = 0.5) +
    geom_text(aes(label = round(Correlation, 2)), size = 4, fontface = "bold") +
    scale_fill_gradient2(low = rda_colors$primary, mid = "white", high = rda_colors$secondary,
                        midpoint = 0, limits = c(-1, 1)) +
    labs(
      title = "土壤属性变化量相关性矩阵",
      x = "土壤属性",
      y = "土壤属性",
      fill = "相关系数"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12, angle = 45, hjust = 1),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 11),
      panel.grid = element_blank()
    ) +
    coord_fixed()
  
  ggsave(output_path, plot = p, width = 10, height = 8, dpi = 300, bg = "white")
  cat("✅ 响应变量相关性热图已保存:", output_path, "\n")

  return(p)
}

# 创建环境变量分组箱线图
create_env_variables_boxplot <- function(processed_data, output_path) {

  # 变量分类
  var_classification <- classify_variables(processed_data$env_vars)

  # 准备数据
  env_data <- processed_data$data[, processed_data$env_vars, drop = FALSE]
  env_data <- env_data[complete.cases(env_data), ]

  # 转换为长格式
  env_long <- env_data %>%
    pivot_longer(cols = everything(), names_to = "Variable", values_to = "Value") %>%
    mutate(
      Group = case_when(
        Variable %in% var_classification$groups$climate ~ "气候",
        Variable %in% var_classification$groups$terrain ~ "地形",
        Variable %in% var_classification$groups$vegetation ~ "植被",
        Variable %in% var_classification$groups$human ~ "人类活动",
        Variable %in% var_classification$groups$soil_3d ~ "3D土壤",
        Variable %in% var_classification$groups$categorical ~ "分类变量",
        TRUE ~ "其他"
      )
    )

  # 标准化数值（分组内）
  env_long <- env_long %>%
    group_by(Group, Variable) %>%
    mutate(Value_scaled = scale(Value)[,1]) %>%
    ungroup()

  p <- ggplot(env_long, aes(x = Group, y = Value_scaled, fill = Group)) +
    geom_boxplot(alpha = 0.7, outlier.alpha = 0.5) +
    scale_fill_manual(values = rda_colors$gradient) +
    labs(
      title = "环境变量分组分布特征",
      x = "变量组",
      y = "标准化值",
      fill = "变量组"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12),
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none",
      panel.grid.minor = element_blank()
    )

  ggsave(output_path, plot = p, width = 12, height = 8, dpi = 300, bg = "white")
  cat("✅ 环境变量分组箱线图已保存:", output_path, "\n")

  return(p)
}

# 创建土壤属性变化雷达图
create_soil_change_radar <- function(processed_data, output_path) {

  # 计算各土壤属性的平均变化量
  response_data <- processed_data$data[, processed_data$response_vars, drop = FALSE]
  response_data <- response_data[complete.cases(response_data), ]

  # 计算统计量
  stats_data <- response_data %>%
    summarise_all(list(
      mean = ~mean(., na.rm = TRUE),
      sd = ~sd(., na.rm = TRUE),
      median = ~median(., na.rm = TRUE)
    )) %>%
    pivot_longer(cols = everything(), names_to = "Variable", values_to = "Value") %>%
    separate(Variable, into = c("Soil_Property", "Statistic"), sep = "_(?=[^_]*$)") %>%
    pivot_wider(names_from = Statistic, values_from = Value)

  # 标准化数据用于雷达图
  stats_data$mean_scaled <- scale(stats_data$mean)[,1]
  stats_data$sd_scaled <- scale(stats_data$sd)[,1]

  # 创建雷达图数据
  radar_data <- data.frame(
    Property = stats_data$Soil_Property,
    Mean_Change = stats_data$mean_scaled,
    Variability = stats_data$sd_scaled
  )

  # 转换为极坐标系数据
  radar_long <- radar_data %>%
    pivot_longer(cols = c(Mean_Change, Variability), names_to = "Metric", values_to = "Value") %>%
    mutate(
      Angle = rep(seq(0, 2*pi, length.out = nrow(radar_data) + 1)[1:nrow(radar_data)], 2),
      x = Value * cos(Angle),
      y = Value * sin(Angle)
    )

  p <- ggplot(radar_long, aes(x = x, y = y, color = Metric, group = Metric)) +
    geom_polygon(alpha = 0.3, fill = NA, size = 1.2) +
    geom_point(size = 3) +
    geom_text_repel(aes(label = Property), size = 4, fontface = "bold") +
    scale_color_manual(values = c("Mean_Change" = rda_colors$primary,
                                 "Variability" = rda_colors$secondary)) +
    labs(
      title = "土壤属性变化特征雷达图",
      color = "指标"
    ) +
    theme_void() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 11),
      legend.position = "bottom"
    ) +
    coord_fixed()

  ggsave(output_path, plot = p, width = 10, height = 10, dpi = 300, bg = "white")
  cat("✅ 土壤属性变化雷达图已保存:", output_path, "\n")

  return(p)
}

# 主可视化函数
create_publication_plots <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n🎨 === 创建论文级图表 ===\n")

  if(analysis_type == "stratified") {
    # 分层分析图表
    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result)) next

      # RDA双序图
      biplot_path <- file.path(output_dir, paste0("RDA双序图_", layer_name, ".png"))
      create_rda_biplot(result, paste0("RDA双序图 - ", layer_name), biplot_path)

      # 变量重要性图
      importance_path <- file.path(output_dir, paste0("变量重要性_", layer_name, ".png"))
      create_variable_importance_plot(result, importance_path)
    }

    # 解释度对比图
    variance_path <- file.path(output_dir, "分层RDA解释度对比.png")
    create_variance_barplot(rda_results, variance_path, "stratified")

  } else {
    # 整体分析图表
    if(!is.null(rda_results)) {
      # RDA双序图
      biplot_path <- file.path(output_dir, "RDA双序图_整体.png")
      create_rda_biplot(rda_results, "RDA双序图 - 整体数据", biplot_path)

      # 变量重要性图
      importance_path <- file.path(output_dir, "变量重要性_整体.png")
      create_variable_importance_plot(rda_results, importance_path)

      # 各轴解释度图
      variance_path <- file.path(output_dir, "RDA轴解释度.png")
      create_variance_barplot(rda_results, variance_path, "overall")
    }
  }

  # 通用图表
  # 响应变量相关性热图
  correlation_path <- file.path(output_dir, "土壤属性相关性热图.png")
  create_response_correlation_heatmap(processed_data, correlation_path)

  # 环境变量分组箱线图
  boxplot_path <- file.path(output_dir, "环境变量分组分布.png")
  create_env_variables_boxplot(processed_data, boxplot_path)

  # 土壤属性变化雷达图
  radar_path <- file.path(output_dir, "土壤属性变化雷达图.png")
  create_soil_change_radar(processed_data, radar_path)

  cat("🎉 所有图表创建完成！\n")
}
