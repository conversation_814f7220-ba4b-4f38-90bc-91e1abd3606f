# =============================================================================
# 论文级高质量可视化函数
# =============================================================================

# 定义颜色方案
rda_colors <- list(
  primary = "#2E86AB",
  secondary = "#A23B72",
  accent = "#F18F01",
  warning = "#C73E1D",
  success = "#6A994E",
  neutral = "#577590",
  gradient = c("#2E86AB", "#4A90A4", "#689B9D", "#86A596", "#A4B08F", "#C2BA88"),
  soil_vars = c("#8B4513", "#CD853F", "#DEB887", "#F4A460", "#D2691E"),
  env_vars = c("#228B22", "#32CD32", "#90EE90", "#98FB98", "#00FF7F")
)

# 创建标准RDA双序图（参考文献格式）
create_rda_biplot <- function(rda_result, title = "RDA双序图", output_path) {

  if(is.null(rda_result) || length(rda_result$rda_model$CCA$eig) == 0) {
    cat("⚠️ RDA结果无效，跳过双序图绘制\n")
    return(NULL)
  }

  # 计算轴解释度
  eigenvals <- eigenvals(rda_result$rda_model)
  total_var <- sum(eigenvals)
  if(length(eigenvals) >= 2) {
    axis1_var <- round(eigenvals[1] / total_var * 100, 2)
    axis2_var <- round(eigenvals[2] / total_var * 100, 2)
  } else {
    axis1_var <- round(eigenvals[1] / total_var * 100, 2)
    axis2_var <- 0
  }

  # 使用标准RDA双序图
  png(output_path, width = 10, height = 8, units = "in", res = 300)

  # 设置图形参数
  par(mar = c(5, 5, 4, 2))

  # 使用vegan的标准双序图，但自定义样式
  plot(rda_result$rda_model, type = "n",
       xlab = paste0("RDA1 (", axis1_var, "%)"),
       ylab = paste0("RDA2 (", axis2_var, "%)"),
       main = title,
       cex.lab = 1.3, cex.axis = 1.1, cex.main = 1.4)

  # 添加网格线
  abline(h = 0, v = 0, col = "gray70", lty = 2)

  # 添加样点（蓝色圆圈）
  points(rda_result$rda_model, display = "sites", pch = 21,
         bg = "lightblue", col = "blue", cex = 1.0)

  # 添加响应变量箭头（红色，较粗）
  arrows(0, 0,
         scores(rda_result$rda_model, display = "species", choices = 1)[,1],
         scores(rda_result$rda_model, display = "species", choices = 2)[,1],
         col = "red", length = 0.1, lwd = 2.5)

  # 添加响应变量标签（红色）
  text(rda_result$rda_model, display = "species",
       col = "red", cex = 1.0, font = 2)

  # 选择重要的环境变量并绘制
  tryCatch({
    if(!is.null(rda_result$explanatory_data)) {
      # 使用envfit选择重要变量
      env_fit <- envfit(rda_result$rda_model, rda_result$explanatory_data, permutations = 999)

      # 选择显著且相关性强的变量
      significant_vars <- env_fit$vectors$pvals < 0.05
      strong_vars <- env_fit$vectors$r > 0.15  # 降低阈值以显示更多变量
      important_vars <- significant_vars & strong_vars

      # 如果重要变量太少，选择前10个最强的
      if(sum(important_vars) < 5) {
        r_squared <- env_fit$vectors$r
        top_vars <- order(r_squared, decreasing = TRUE)[1:min(10, length(r_squared))]
        important_vars <- rep(FALSE, length(r_squared))
        important_vars[top_vars] <- TRUE
      }

      # 绘制重要的环境变量箭头（蓝色）
      if(sum(important_vars) > 0) {
        plot(env_fit, p.max = 0.05, col = "blue", cex = 0.9,
             choices = 1:2, add = TRUE)

        cat("   显示", sum(important_vars), "个重要环境变量\n")
      }
    } else {
      # 备用方案：显示所有环境变量
      text(rda_result$rda_model, display = "bp",
           col = "blue", cex = 0.9)
    }
  }, error = function(e) {
    # 出错时显示所有环境变量
    text(rda_result$rda_model, display = "bp",
         col = "blue", cex = 0.9)
  })

  # 添加图例
  legend("topright",
         legend = c("样本点", "土壤属性", "环境变量"),
         col = c("blue", "red", "blue"),
         pch = c(21, NA, NA),
         lty = c(NA, 1, 1),
         pt.bg = c("lightblue", NA, NA),
         cex = 1.0, bg = "white")

  dev.off()

  cat("✅ RDA双序图已保存:", output_path, "\n")
  return(NULL)
}

# 创建变量重要性图
create_variable_importance_plot <- function(rda_result, output_path) {

  if(is.null(rda_result$explanatory_data)) {
    cat("⚠️ 缺少环境变量数据，跳过重要性图绘制\n")
    return(NULL)
  }

  # 使用envfit计算变量重要性
  env_fit <- envfit(rda_result$rda_model, rda_result$explanatory_data, permutations = 999)

  # 提取重要性数据
  importance_data <- data.frame(
    Variable = names(env_fit$vectors$r),
    R_squared = env_fit$vectors$r,
    P_value = env_fit$vectors$pvals,
    Significant = env_fit$vectors$pvals < 0.05
  )

  # 按重要性排序
  importance_data <- importance_data[order(importance_data$R_squared, decreasing = TRUE), ]
  importance_data$Variable <- factor(importance_data$Variable, levels = importance_data$Variable)

  # 创建图表
  png(output_path, width = 10, height = 6, units = "in", res = 300)

  par(mar = c(8, 5, 4, 2))

  # 创建条形图
  colors <- ifelse(importance_data$Significant, "darkgreen", "gray70")
  barplot(importance_data$R_squared,
          names.arg = importance_data$Variable,
          col = colors,
          main = "环境变量重要性 (R²)",
          ylab = "R² 值",
          las = 2,  # 垂直标签
          cex.names = 0.8,
          cex.lab = 1.2,
          cex.main = 1.3)

  # 添加显著性线
  abline(h = 0.1, col = "red", lty = 2, lwd = 2)
  text(x = length(importance_data$Variable) * 0.8, y = 0.12,
       "R² = 0.1", col = "red", cex = 1.0)

  # 添加图例
  legend("topright",
         legend = c("显著 (p < 0.05)", "不显著"),
         fill = c("darkgreen", "gray70"),
         cex = 1.0)

  dev.off()

  cat("✅ 变量重要性图已保存:", basename(output_path), "\n")
  return(importance_data)
    
    # 设置标签和主题
    labs(
      title = title,
      subtitle = paste0("解释度: ", round(rda_result$explained_variance, 1), "%"),
      x = paste0("RDA1 (", axis1_var, "%)"),
      y = paste0("RDA2 (", axis2_var, "%)")
    ) +
    
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 14, hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12),
      panel.grid.minor = element_blank(),
      legend.position = "none"
    ) +
    
    coord_fixed(ratio = 1)
  
  # 保存图片
  ggsave(output_path, plot = p, width = 12, height = 10, dpi = 300, bg = "white")
  cat("✅ RDA双序图已保存:", output_path, "\n")
  
  return(p)
}

# 创建解释度条形图
create_variance_barplot <- function(rda_results, output_path, analysis_type = "stratified") {

  if(analysis_type == "stratified") {
    # 检查RDA结果
    if(is.null(rda_results) || length(rda_results) == 0) {
      cat("⚠️ 没有有效的RDA结果，跳过解释度条形图\n")
      return(NULL)
    }

    # 分层结果
    variance_data <- data.frame(
      Layer = names(rda_results),
      Variance = sapply(rda_results, function(x) {
        if(is.null(x) || is.null(x$explained_variance)) {
          return(0)
        }
        return(as.numeric(x$explained_variance))
      }),
      stringsAsFactors = FALSE
    )

    # 移除无效数据
    variance_data <- variance_data[!is.na(variance_data$Variance), ]

    if(nrow(variance_data) == 0) {
      cat("⚠️ 没有有效的解释度数据，跳过条形图\n")
      return(NULL)
    }

    # 重新排序深度层
    variance_data$Layer <- factor(variance_data$Layer,
                                 levels = c("0-10cm", "10-30cm", "30-60cm", "60-100cm"))

    cat("📊 解释度数据:\n")
    print(variance_data)

    p <- ggplot(variance_data, aes(x = Layer, y = Variance, fill = Layer)) +
      geom_col(alpha = 0.8, width = 0.7) +
      geom_text(aes(label = paste0(round(Variance, 1), "%")), 
                vjust = -0.5, size = 5, fontface = "bold") +
      scale_fill_manual(values = rda_colors$gradient[1:4]) +
      labs(
        title = "各深度层RDA解释度对比",
        x = "深度层",
        y = "解释度 (%)"
      ) +
      theme_bw() +
      theme(
        plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
        axis.title = element_text(size = 14, face = "bold"),
        axis.text = element_text(size = 12),
        legend.position = "none",
        panel.grid.minor = element_blank()
      ) +
      ylim(0, max(variance_data$Variance) * 1.2)
    
  } else {
    # 整体结果 - 显示各轴解释度
    if(length(rda_results$axis_variance) > 0) {
      axis_data <- data.frame(
        Axis = names(rda_results$axis_variance),
        Variance = as.numeric(rda_results$axis_variance),
        stringsAsFactors = FALSE
      )
      
      p <- ggplot(axis_data, aes(x = Axis, y = Variance, fill = Axis)) +
        geom_col(alpha = 0.8, width = 0.7) +
        geom_text(aes(label = paste0(round(Variance, 1), "%")), 
                  vjust = -0.5, size = 5, fontface = "bold") +
        scale_fill_manual(values = rda_colors$gradient[1:nrow(axis_data)]) +
        labs(
          title = "RDA各轴解释度",
          x = "RDA轴",
          y = "解释度 (%)"
        ) +
        theme_bw() +
        theme(
          plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
          axis.title = element_text(size = 14, face = "bold"),
          axis.text = element_text(size = 12),
          legend.position = "none",
          panel.grid.minor = element_blank()
        ) +
        ylim(0, max(axis_data$Variance) * 1.2)
    } else {
      return(NULL)
    }
  }
  
  ggsave(output_path, plot = p, width = 10, height = 8, dpi = 300, bg = "white")
  cat("✅ 解释度条形图已保存:", output_path, "\n")
  
  return(p)
}

# 创建变量重要性图
create_variable_importance_plot <- function(rda_result, output_path, top_n = 15) {
  
  if(is.null(rda_result) || is.null(rda_result$terms_test)) {
    cat("⚠️ 无法创建变量重要性图\n")
    return(NULL)
  }
  
  # 提取变量显著性数据
  terms_data <- rda_result$terms_test
  terms_data$Variable <- rownames(terms_data)
  terms_data$Significance <- ifelse(terms_data$`Pr(>F)` < 0.001, "***",
                                   ifelse(terms_data$`Pr(>F)` < 0.01, "**",
                                         ifelse(terms_data$`Pr(>F)` < 0.05, "*", "ns")))
  
  # 按F值排序，取前top_n个
  terms_data <- terms_data[order(terms_data$F, decreasing = TRUE), ]
  if(nrow(terms_data) > top_n) {
    terms_data <- terms_data[1:top_n, ]
  }
  
  # 重新排序用于绘图
  terms_data$Variable <- factor(terms_data$Variable, levels = terms_data$Variable)
  
  p <- ggplot(terms_data, aes(x = reorder(Variable, F), y = F, fill = Significance)) +
    geom_col(alpha = 0.8) +
    geom_text(aes(label = Significance), hjust = -0.2, size = 4, fontface = "bold") +
    scale_fill_manual(values = c("***" = rda_colors$warning, 
                                "**" = rda_colors$accent,
                                "*" = rda_colors$success,
                                "ns" = rda_colors$neutral)) +
    labs(
      title = paste0("变量重要性排序 - ", rda_result$layer_name),
      x = "环境变量",
      y = "F值",
      fill = "显著性"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text.x = element_text(size = 10, angle = 45, hjust = 1),
      axis.text.y = element_text(size = 12),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 11),
      panel.grid.minor = element_blank()
    ) +
    coord_flip()
  
  ggsave(output_path, plot = p, width = 12, height = 8, dpi = 300, bg = "white")
  cat("✅ 变量重要性图已保存:", output_path, "\n")
  
  return(p)
}

# 创建响应变量相关性热图
create_response_correlation_heatmap <- function(processed_data, output_path) {
  
  # 提取响应变量数据
  response_data <- processed_data$data[, processed_data$response_vars, drop = FALSE]
  response_data <- response_data[complete.cases(response_data), ]
  
  # 计算相关性矩阵
  cor_matrix <- cor(response_data, use = "complete.obs")
  
  # 转换为长格式数据
  cor_data <- expand.grid(Var1 = rownames(cor_matrix), Var2 = colnames(cor_matrix))
  cor_data$Correlation <- as.vector(cor_matrix)
  
  # 创建热图
  p <- ggplot(cor_data, aes(x = Var1, y = Var2, fill = Correlation)) +
    geom_tile(color = "white", size = 0.5) +
    geom_text(aes(label = round(Correlation, 2)), size = 4, fontface = "bold") +
    scale_fill_gradient2(low = rda_colors$primary, mid = "white", high = rda_colors$secondary,
                        midpoint = 0, limits = c(-1, 1)) +
    labs(
      title = "土壤属性变化量相关性矩阵",
      x = "土壤属性",
      y = "土壤属性",
      fill = "相关系数"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12, angle = 45, hjust = 1),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 11),
      panel.grid = element_blank()
    ) +
    coord_fixed()
  
  ggsave(output_path, plot = p, width = 10, height = 8, dpi = 300, bg = "white")
  cat("✅ 响应变量相关性热图已保存:", output_path, "\n")

  return(p)
}

# 创建环境变量分组箱线图
create_env_variables_boxplot <- function(processed_data, output_path) {

  # 变量分类
  var_classification <- classify_variables(processed_data$env_vars)

  # 准备数据
  env_data <- processed_data$data[, processed_data$env_vars, drop = FALSE]
  env_data <- env_data[complete.cases(env_data), ]

  # 转换为长格式
  env_long <- env_data %>%
    pivot_longer(cols = everything(), names_to = "Variable", values_to = "Value") %>%
    mutate(
      Group = case_when(
        Variable %in% var_classification$groups$climate ~ "气候",
        Variable %in% var_classification$groups$terrain ~ "地形",
        Variable %in% var_classification$groups$vegetation ~ "植被",
        Variable %in% var_classification$groups$human ~ "人类活动",
        Variable %in% var_classification$groups$soil_3d ~ "3D土壤",
        Variable %in% var_classification$groups$categorical ~ "分类变量",
        TRUE ~ "其他"
      )
    )

  # 标准化数值（分组内）
  env_long <- env_long %>%
    group_by(Group, Variable) %>%
    mutate(Value_scaled = scale(Value)[,1]) %>%
    ungroup()

  p <- ggplot(env_long, aes(x = Group, y = Value_scaled, fill = Group)) +
    geom_boxplot(alpha = 0.7, outlier.alpha = 0.5) +
    scale_fill_manual(values = rda_colors$gradient) +
    labs(
      title = "环境变量分组分布特征",
      x = "变量组",
      y = "标准化值",
      fill = "变量组"
    ) +
    theme_bw() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12),
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none",
      panel.grid.minor = element_blank()
    )

  ggsave(output_path, plot = p, width = 12, height = 8, dpi = 300, bg = "white")
  cat("✅ 环境变量分组箱线图已保存:", output_path, "\n")

  return(p)
}

# 创建土壤属性变化雷达图
create_soil_change_radar <- function(processed_data, output_path) {

  # 计算各土壤属性的平均变化量
  response_data <- processed_data$data[, processed_data$response_vars, drop = FALSE]
  response_data <- response_data[complete.cases(response_data), ]

  # 计算统计量
  stats_data <- response_data %>%
    summarise_all(list(
      mean = ~mean(., na.rm = TRUE),
      sd = ~sd(., na.rm = TRUE),
      median = ~median(., na.rm = TRUE)
    )) %>%
    pivot_longer(cols = everything(), names_to = "Variable", values_to = "Value") %>%
    separate(Variable, into = c("Soil_Property", "Statistic"), sep = "_(?=[^_]*$)") %>%
    pivot_wider(names_from = Statistic, values_from = Value)

  # 标准化数据用于雷达图
  stats_data$mean_scaled <- scale(stats_data$mean)[,1]
  stats_data$sd_scaled <- scale(stats_data$sd)[,1]

  # 创建雷达图数据
  radar_data <- data.frame(
    Property = stats_data$Soil_Property,
    Mean_Change = stats_data$mean_scaled,
    Variability = stats_data$sd_scaled
  )

  # 转换为极坐标系数据
  radar_long <- radar_data %>%
    pivot_longer(cols = c(Mean_Change, Variability), names_to = "Metric", values_to = "Value") %>%
    mutate(
      Angle = rep(seq(0, 2*pi, length.out = nrow(radar_data) + 1)[1:nrow(radar_data)], 2),
      x = Value * cos(Angle),
      y = Value * sin(Angle)
    )

  p <- ggplot(radar_long, aes(x = x, y = y, color = Metric, group = Metric)) +
    geom_polygon(alpha = 0.3, fill = NA, size = 1.2) +
    geom_point(size = 3) +
    geom_text_repel(aes(label = Property), size = 4, fontface = "bold") +
    scale_color_manual(values = c("Mean_Change" = rda_colors$primary,
                                 "Variability" = rda_colors$secondary)) +
    labs(
      title = "土壤属性变化特征雷达图",
      color = "指标"
    ) +
    theme_void() +
    theme(
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 11),
      legend.position = "bottom"
    ) +
    coord_fixed()

  ggsave(output_path, plot = p, width = 10, height = 10, dpi = 300, bg = "white")
  cat("✅ 土壤属性变化雷达图已保存:", output_path, "\n")

  return(p)
}

# 主可视化函数
create_publication_plots <- function(rda_results, processed_data, output_dir, analysis_type = "overall") {
  cat("\n🎨 === 创建论文级图表 ===\n")

  if(analysis_type == "stratified") {
    # 分层分析图表
    cat("📊 创建分层分析图表...\n")
    valid_results <- 0

    for(layer_name in names(rda_results)) {
      result <- rda_results[[layer_name]]
      if(is.null(result) || is.null(result$rda_model)) {
        cat("  ⚠️ 跳过", layer_name, "（无有效结果）\n")
        next
      }

      valid_results <- valid_results + 1
      cat("  处理深度层:", layer_name, "\n")

      # 只创建RDA双序图（核心图表）
      tryCatch({
        biplot_path <- file.path(output_dir, paste0("RDA双序图_", layer_name, ".png"))
        create_rda_biplot(result, paste0("RDA双序图 - ", layer_name), biplot_path)
      }, error = function(e) {
        cat("    ⚠️ 创建", layer_name, "双序图失败:", e$message, "\n")
      })
    }

    cat("  有效结果数:", valid_results, "/", length(rda_results), "\n")

    # 解释度对比图
    if(valid_results > 1) {
      tryCatch({
        variance_path <- file.path(output_dir, "分层RDA解释度对比.png")
        create_variance_barplot(rda_results, variance_path, "stratified")
      }, error = function(e) {
        cat("  ⚠️ 创建解释度对比图失败:", e$message, "\n")
      })
    }

  } else {
    # 整体分析图表
    cat("📊 创建整体分析图表...\n")
    if(!is.null(rda_results) && !is.null(rda_results$rda_model)) {
      # 只创建RDA双序图
      tryCatch({
        biplot_path <- file.path(output_dir, "RDA双序图_整体.png")
        create_rda_biplot(rda_results, "RDA双序图 - 整体数据", biplot_path)
      }, error = function(e) {
        cat("  ⚠️ 创建整体双序图失败:", e$message, "\n")
      })
    } else {
      cat("  ⚠️ 整体RDA结果无效\n")
    }
  }

  # 通用图表
  cat("\n📊 === 生成通用分析图表 ===\n")

  # 响应变量相关性热图
  correlation_path <- file.path(output_dir, "土壤属性相关性热图.png")
  create_response_correlation_heatmap(processed_data, correlation_path)

  # 环境变量分组箱线图
  boxplot_path <- file.path(output_dir, "环境变量分组分布.png")
  create_env_variables_boxplot(processed_data, boxplot_path)

  # 土壤属性变化雷达图
  radar_path <- file.path(output_dir, "土壤属性变化雷达图.png")
  create_soil_change_radar(processed_data, radar_path)

  # 变量重要性图（如果是整体分析）
  if(analysis_type == "overall" && !is.null(rda_results$explanatory_data)) {
    importance_path <- file.path(output_dir, "环境变量重要性分析.png")
    create_variable_importance_plot(rda_results, importance_path)
  }

  cat("✅ 核心图表创建完成！\n")
}
