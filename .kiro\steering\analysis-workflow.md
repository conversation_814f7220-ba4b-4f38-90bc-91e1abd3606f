# 土壤属性变化驱动力分析工作流程

## 分析目标
将静态比较转为动态变化分析，量化各驱动因子对土壤属性变化的贡献度。

## 当前实际进展状态

### 已完成：空间插补系统 (Chapter 3)
- ✅ **三种方法对比系统**：普通克里金(OK)、回归克里金(RK)、随机森林(RF)
- ✅ **自动参数调优**：基于交叉验证的参数优化
- ✅ **智能方法选择**：R²优先，接近时比较RMSE
- ✅ **完整评估体系**：R²、RMSE、MAE、CCC、RPD
- ✅ **系统化流程**：缺失值分析→建模→填补→验证
- ✅ **质量控制**：邻近层协变量、分层保底策略
- ✅ **输出标准化**：Excel格式结果和性能报告

### 系统技术特点
- **模块化设计**：统一建模接口，易于扩展
- **智能数据准备**：自动处理环境变量和邻近层变量
- **稳健性保证**：多重回退策略确保填补成功
- **可追溯性**：详细的填补日志和方法记录

## 核心分析步骤

### 第一步：空间插补完成验证 ✅
**目标**：确保所有县域的土壤属性数据完整性

#### 已实现功能
- 5县×6属性×4深度×2年份的系统化插补
- 三种方法自动对比和最优选择
- 插补质量评估和验证
- 完整数据集输出

### 第二步：变化量计算
**目标**：将静态比较转为动态变化分析

#### 计算所有变量在两个时期的差值 (Δ)
- **Δ土壤属性** = 属性_2023 - 属性_1980
  - ΔpH = pH_2023 - pH_1980
  - ΔSOM = SOM_2023 - SOM_1980
  - ΔTN = TN_2023 - TN_1980
  - ΔTP = TP_2023 - TP_1980
  - Δ物理粘粒 = 物理粘粒_2023 - 物理粘粒_1980
  - Δ物理砂粒 = 物理砂粒_2023 - 物理砂粒_1980

- **Δ环境因子** = 因子_2023 - 因子_1980
  - Δ温度、Δ降水、Δ海拔、Δ坡度、Δ坡向
  - ΔNDVI、Δ夜间灯光、Δ人口密度、ΔGDP等

- **Δ黑土层厚度** = 厚度_2023 - 厚度_1980

#### 重要原则
- **后续所有分析都将基于这张全新的"变化量"数据集**
- 变化量数据集是动态分析的基础，不再使用静态时点数据

### 第三步：驱动因子筛选 (降维)
**目标**：从几十个Δ环境因子中，筛选出信息量大且不冗余的关键驱动因子

#### 技术选型
- **采用方差膨胀因子 (VIF) 剔除法**
- **VIF阈值**：VIF > 10 的变量将被剔除

#### 选择VIF的理由
1. **解决多重共线性**：VIF能有效识别和剔除高度相关的变量
2. **保持变量可解释性**：保留下来的变量仍是原始的、具有明确物理意义的变量（如Δ温度）
3. **便于结果解释**：避免使用主成分分析(PCA)，因为其合成的"主成分"缺乏现实意义，难以解释

#### VIF筛选流程
1. 计算所有Δ环境因子的VIF值
2. 识别VIF > 10的变量
3. 逐步剔除VIF最高的变量
4. 重新计算剩余变量的VIF
5. 重复直到所有变量VIF < 10

### 第四步：归因建模 (核心分析)
**目标**：量化各驱动因子对土壤属性变化的贡献度

#### 数据准备策略
1. **数据合并 (Pool)**：将五个县插补并计算完Δ值后的数据合并成一个大数据集
2. **分类变量处理 (独热编码)**：

##### 土地利用变化处理
- 创建"1980类型->2023类型"的转移变量
- 编码为多个0/1变量 (如is_forest_to_farm, is_farm_to_urban等)
- 捕捉土地利用转换模式的影响

##### 土壤类型变化处理
- 创建"1980类型->2023类型"的转移变量
- 编码为多个0/1变量
- 反映土壤类型演变对属性变化的影响

##### 县域归属处理
- 创建County变量，标记每个数据点来源的县
- 用于控制县域间的基线差异

#### 模型选型：冗余分析 (RDA) + 混合效应
**采用冗余分析 (Redundancy Analysis, RDA)，并结合混合效应思想**

##### 变量设置
- **响应变量 (Y)**：
  - Δ土壤属性1, Δ土壤属性2, Δ土壤属性3, ...
  - 多元响应变量矩阵

- **解释变量 (X)**：
  - 筛选后的Δ环境因子（通过VIF筛选）
  - 编码后的土地利用变化变量
  - 编码后的土壤类型变化变量
  - Δ黑土层厚度

- **协变量/随机效应**：
  - County变量，用以控制各县间的基线差异
  - 处理县域空间异质性

#### 模型优势
1. **多元响应**：RDA能同时处理多个土壤属性的变化
2. **变量筛选**：能识别对土壤变化贡献最大的驱动因子
3. **贡献量化**：提供各驱动因子的解释度和显著性
4. **空间控制**：通过County变量控制空间异质性
5. **结果可视化**：RDA双序图便于结果解释

## 分析流程控制原则

### 数据质量保证
- 确保变化量计算的准确性
- VIF筛选过程的系统性记录
- 分类变量编码的一致性检查

### 模型验证要求
- RDA模型的显著性检验
- 解释度评估 (R²调整)
- 残差分析和模型诊断
- 交叉验证稳健性检查

### 结果解释框架
- 驱动因子贡献度排序
- 显著性水平标注
- 县域差异性分析
- 生态学意义解释

## 预期输出成果

### 已完成成果 ✅
- **完整插补数据集**：5县×6属性×4深度×2年份无缺失值数据
- **插补性能报告**：详细的方法对比和精度评估
- **质量验证报告**：插补结果的空间合理性验证
- **技术文档**：完整的插补方法和参数记录

### 待完成数据产品
- 完整的变化量数据集 (Δ值数据集)
- VIF筛选后的关键驱动因子列表
- 编码后的分类变量数据集

### 待完成分析结果
- RDA模型统计结果表
- 驱动因子贡献度量化表
- RDA双序图可视化
- 县域差异性分析报告

### 学术价值
- **已实现**：小样本条件下空间插补方法对比研究
- **待实现**：动态变化分析方法的创新应用
- **待实现**：多驱动因子综合归因的量化结果
- **待实现**：县域尺度土壤变化机制的科学认识

## 系统扩展建议

### 轻量级不确定性量化（可选）
如需要量化插补不确定性，建议使用Bootstrap方法：
- 工作量增加仅20%
- 保持现有系统架构
- 为RDA分析提供不确定性权重
- 提升论文方法创新性

### 插补质量进一步验证
- 空间自相关检验：确保插补结果符合土壤空间分布规律
- 交叉验证升级：使用空间交叉验证替代随机交叉验证
- 插补合理性检查：验证插补值是否在合理范围内