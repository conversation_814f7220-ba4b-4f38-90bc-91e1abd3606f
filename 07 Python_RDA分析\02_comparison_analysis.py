#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
R vs Python RDA分析结果对比
作者: AI Assistant
日期: 2025-01-29

功能:
1. 对比R和Python的RDA分析结果
2. 分析方法差异
3. 结果可视化对比
4. 生成对比报告
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class RDAComparison:
    """R vs Python RDA结果对比类"""
    
    def __init__(self, output_dir="RDA对比分析"):
        self.output_dir = output_dir
        Path(output_dir).mkdir(exist_ok=True)
        print(f"📁 对比分析输出目录: {output_dir}")
    
    def load_r_results(self, r_excel_path, r_summary_path):
        """加载R分析结果"""
        print("\n📊 === 加载R分析结果 ===")
        
        try:
            # 读取R的Excel结果
            self.r_basic = pd.read_excel(r_excel_path, sheet_name='1-基本统计')
            self.r_importance = pd.read_excel(r_excel_path, sheet_name='3-变量重要性')
            
            # 读取R的文本摘要
            with open(r_summary_path, 'r', encoding='utf-8') as f:
                self.r_summary = f.read()
            
            print("✅ R结果加载成功")
            print(f"   - 基本统计: {self.r_basic.shape}")
            print(f"   - 变量重要性: {self.r_importance.shape}")
            
        except Exception as e:
            print(f"⚠️ R结果加载失败: {e}")
            self.r_basic = None
            self.r_importance = None
            self.r_summary = None
        
        return self
    
    def load_python_results(self, py_excel_path, py_summary_path):
        """加载Python分析结果"""
        print("\n🐍 === 加载Python分析结果 ===")
        
        try:
            # 读取Python的Excel结果
            self.py_basic = pd.read_excel(py_excel_path, sheet_name='基本统计')
            self.py_importance = pd.read_excel(py_excel_path, sheet_name='变量重要性')
            
            # 读取Python的文本摘要
            with open(py_summary_path, 'r', encoding='utf-8') as f:
                self.py_summary = f.read()
            
            print("✅ Python结果加载成功")
            print(f"   - 基本统计: {self.py_basic.shape}")
            print(f"   - 变量重要性: {self.py_importance.shape}")
            
        except Exception as e:
            print(f"⚠️ Python结果加载失败: {e}")
            self.py_basic = None
            self.py_importance = None
            self.py_summary = None
        
        return self
    
    def compare_basic_stats(self):
        """对比基本统计"""
        print("\n📈 === 基本统计对比 ===")
        
        if self.r_basic is None or self.py_basic is None:
            print("⚠️ 缺少基本统计数据，跳过对比")
            return self
        
        # 提取关键指标
        comparison_data = []
        
        # R结果
        r_explained = float(self.r_basic[self.r_basic['项目'] == '总解释度(%)']['值'].iloc[0])
        r_samples = int(self.r_basic[self.r_basic['项目'] == '样本数量']['值'].iloc[0])
        
        # Python结果
        py_explained = float(self.py_basic[self.py_basic['项目'] == '总解释度(%)']['值'].iloc[0])
        py_samples = int(self.py_basic[self.py_basic['项目'] == '样本数量']['值'].iloc[0])
        
        comparison_data = {
            '指标': ['总解释度(%)', '样本数量', '分析方法'],
            'R版本': [r_explained, r_samples, 'vegan::rda'],
            'Python版本': [py_explained, py_samples, 'sklearn::CCA'],
            '差异': [py_explained - r_explained, py_samples - r_samples, '方法不同']
        }
        
        self.comparison_df = pd.DataFrame(comparison_data)
        
        print("📊 基本统计对比:")
        print(self.comparison_df.to_string(index=False))
        
        # 可视化对比
        self._plot_basic_comparison(r_explained, py_explained)
        
        return self
    
    def _plot_basic_comparison(self, r_explained, py_explained):
        """绘制基本统计对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 解释度对比
        methods = ['R (vegan)', 'Python (CCA)']
        explained_vars = [r_explained, py_explained]
        colors = ['#2E86AB', '#F18F01']
        
        bars1 = ax1.bar(methods, explained_vars, color=colors, alpha=0.7)
        ax1.set_ylabel('解释度 (%)', fontsize=12, fontweight='bold')
        ax1.set_title('RDA解释度对比', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for bar, val in zip(bars1, explained_vars):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{val:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 差异分析
        diff = py_explained - r_explained
        ax2.bar(['解释度差异'], [diff], 
               color='green' if diff > 0 else 'red', alpha=0.7)
        ax2.set_ylabel('差异 (%)', fontsize=12, fontweight='bold')
        ax2.set_title('Python vs R 差异', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加差异标签
        ax2.text(0, diff + (0.1 if diff > 0 else -0.1),
                f'{diff:+.1f}%', ha='center', 
                va='bottom' if diff > 0 else 'top', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/基本统计对比.png", dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 基本统计对比图已保存")
    
    def compare_variable_importance(self):
        """对比变量重要性"""
        print("\n🎯 === 变量重要性对比 ===")
        
        if self.r_importance is None or self.py_importance is None:
            print("⚠️ 缺少变量重要性数据，跳过对比")
            return self
        
        # 标准化列名
        r_imp = self.r_importance.copy()
        py_imp = self.py_importance.copy()
        
        # 确保列名一致
        if '环境变量' in r_imp.columns:
            r_imp = r_imp.rename(columns={'环境变量': 'Variable', 'R平方': 'R_squared'})
        if 'Variable' in py_imp.columns:
            py_imp = py_imp.rename(columns={'Importance': 'Py_importance'})
        
        # 合并数据进行对比
        merged = pd.merge(r_imp[['Variable', 'R_squared']], 
                         py_imp[['Variable', 'Py_importance']], 
                         on='Variable', how='outer').fillna(0)
        
        # 计算相关性
        correlation = merged['R_squared'].corr(merged['Py_importance'])
        
        print(f"📊 变量重要性相关性: {correlation:.3f}")
        
        # 可视化对比
        self._plot_importance_comparison(merged, correlation)
        
        # 找出差异最大的变量
        merged['Difference'] = abs(merged['R_squared'] - merged['Py_importance'])
        top_diff = merged.nlargest(5, 'Difference')
        
        print("\n📋 差异最大的5个变量:")
        for _, row in top_diff.iterrows():
            print(f"   {row['Variable']}: R={row['R_squared']:.3f}, Py={row['Py_importance']:.3f}")
        
        self.importance_comparison = merged
        return self
    
    def _plot_importance_comparison(self, merged, correlation):
        """绘制变量重要性对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 散点图对比
        ax1.scatter(merged['R_squared'], merged['Py_importance'], 
                   alpha=0.6, s=60, color='steelblue')
        
        # 添加对角线
        max_val = max(merged['R_squared'].max(), merged['Py_importance'].max())
        ax1.plot([0, max_val], [0, max_val], 'r--', alpha=0.7, label='完全一致线')
        
        ax1.set_xlabel('R版本重要性', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Python版本重要性', fontsize=12, fontweight='bold')
        ax1.set_title(f'变量重要性对比 (相关性: {correlation:.3f})', 
                     fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 前10个变量对比
        top_vars = merged.nlargest(10, 'R_squared')
        
        x = np.arange(len(top_vars))
        width = 0.35
        
        ax2.bar(x - width/2, top_vars['R_squared'], width, 
               label='R版本', color='#2E86AB', alpha=0.7)
        ax2.bar(x + width/2, top_vars['Py_importance'], width,
               label='Python版本', color='#F18F01', alpha=0.7)
        
        ax2.set_xlabel('环境变量', fontsize=12, fontweight='bold')
        ax2.set_ylabel('重要性得分', fontsize=12, fontweight='bold')
        ax2.set_title('前10个重要变量对比', fontsize=14, fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(top_vars['Variable'], rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/变量重要性对比.png", dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 变量重要性对比图已保存")
    
    def generate_comparison_report(self):
        """生成对比报告"""
        print("\n📝 === 生成对比报告 ===")
        
        report_path = f"{self.output_dir}/RDA分析对比报告.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# R vs Python RDA分析对比报告\n\n")
            f.write("## 1. 分析概述\n\n")
            f.write("本报告对比了使用R语言(vegan包)和Python(scikit-learn)进行RDA冗余分析的结果。\n\n")
            
            f.write("## 2. 方法差异\n\n")
            f.write("| 方面 | R版本 | Python版本 |\n")
            f.write("|------|-------|-------------|\n")
            f.write("| 核心包 | vegan::rda | sklearn::CCA |\n")
            f.write("| 算法 | 经典RDA | 典型相关分析 |\n")
            f.write("| 前向选择 | ordistep | 无 |\n")
            f.write("| 显著性检验 | permutation test | 无 |\n\n")
            
            if hasattr(self, 'comparison_df'):
                f.write("## 3. 基本统计对比\n\n")
                f.write(self.comparison_df.to_markdown(index=False))
                f.write("\n\n")
            
            if hasattr(self, 'importance_comparison'):
                corr = self.importance_comparison['R_squared'].corr(self.importance_comparison['Py_importance'])
                f.write("## 4. 变量重要性对比\n\n")
                f.write(f"- **相关性系数**: {corr:.3f}\n")
                f.write(f"- **解释**: {'高度一致' if corr > 0.8 else '中等一致' if corr > 0.5 else '一致性较低'}\n\n")
            
            f.write("## 5. 主要发现\n\n")
            f.write("### 5.1 解释度差异\n")
            if hasattr(self, 'comparison_df'):
                r_exp = self.comparison_df[self.comparison_df['指标'] == '总解释度(%)']['R版本'].iloc[0]
                py_exp = self.comparison_df[self.comparison_df['指标'] == '总解释度(%)']['Python版本'].iloc[0]
                diff = py_exp - r_exp
                f.write(f"- R版本解释度: {r_exp:.1f}%\n")
                f.write(f"- Python版本解释度: {py_exp:.1f}%\n")
                f.write(f"- 差异: {diff:+.1f}%\n\n")
            
            f.write("### 5.2 方法学差异分析\n")
            f.write("1. **算法差异**: R使用经典RDA算法，Python使用CCA近似\n")
            f.write("2. **优化差异**: R有前向选择，Python无自动变量选择\n")
            f.write("3. **统计检验**: R提供置换检验，Python需要额外实现\n\n")
            
            f.write("## 6. 建议\n\n")
            f.write("1. **生态学研究**: 推荐使用R版本，更符合生态学标准\n")
            f.write("2. **机器学习集成**: Python版本更适合与ML流程集成\n")
            f.write("3. **结果验证**: 两种方法可以相互验证分析结果\n")
        
        print(f"✅ 对比报告已保存: {report_path}")
        return self
    
    def export_comparison_excel(self):
        """导出对比Excel"""
        excel_path = f"{self.output_dir}/RDA分析对比结果.xlsx"
        
        with pd.ExcelWriter(excel_path) as writer:
            if hasattr(self, 'comparison_df'):
                self.comparison_df.to_excel(writer, sheet_name='基本统计对比', index=False)
            
            if hasattr(self, 'importance_comparison'):
                self.importance_comparison.to_excel(writer, sheet_name='变量重要性对比', index=False)
        
        print(f"✅ 对比Excel已保存: {excel_path}")
        return self

def main():
    """主函数"""
    print("🔄 === R vs Python RDA分析对比 ===")
    
    # 文件路径
    r_excel = "E:/05 Python/Devway/01 硕士论文/00 原始数据/RDA冗余分析/RDA分析结果.xlsx"
    r_summary = "E:/05 Python/Devway/01 硕士论文/00 原始数据/RDA冗余分析/RDA分析摘要.txt"
    py_excel = "E:/05 Python/Devway/01 硕士论文/07 Python_RDA分析/RDA分析结果_Python版.xlsx"
    py_summary = "E:/05 Python/Devway/01 硕士论文/07 Python_RDA分析/分析摘要_Python版.txt"
    
    # 执行对比分析
    comparison = RDAComparison("E:/05 Python/Devway/01 硕士论文/08 RDA对比分析")
    
    try:
        results = (comparison
                  .load_r_results(r_excel, r_summary)
                  .load_python_results(py_excel, py_summary)
                  .compare_basic_stats()
                  .compare_variable_importance()
                  .generate_comparison_report()
                  .export_comparison_excel())
        
        print("\n🎉 === 对比分析完成 ===")
        print(f"📁 所有结果已保存至: {comparison.output_dir}")
        
        return comparison
        
    except Exception as e:
        print(f"❌ 对比分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
