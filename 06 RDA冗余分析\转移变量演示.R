# =============================================================================
# 转移变量创建演示
# =============================================================================

# 创建示例数据
demo_data <- data.frame(
  ProfileID = paste0("样本", 1:10),
  `LandUse-1980` = c("农田", "农田", "林地", "农田", "林地", "草地", "农田", "林地", "农田", "草地"),
  `LandUse-2023` = c("林地", "农田", "农田", "林地", "林地", "农田", "农田", "林地", "草地", "草地"),
  `Soilclass-1980` = c("黑土", "黑土", "草甸土", "黑土", "草甸土", "黑土", "黑土", "草甸土", "黑土", "黑土"),
  `Soilclass-2023` = c("黑土", "黑土", "黑土", "黑土", "草甸土", "黑土", "黑土", "黑土", "黑土", "黑土"),
  check.names = FALSE
)

cat("=== 原始数据 ===\n")
print(demo_data)

# 演示转移变量创建过程
create_transition_demo <- function(data) {
  cat("\n=== 转移变量创建演示 ===\n")
  
  # 1. 土地利用转移
  cat("\n🏞️ 土地利用转移分析:\n")
  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`
  
  # 创建转移标识
  lu_transitions <- paste(landuse_1980, "→", landuse_2023, sep = "")
  cat("每个样本的转移类型:\n")
  for(i in 1:length(lu_transitions)) {
    cat("  ", data$ProfileID[i], ":", lu_transitions[i], "\n")
  }
  
  # 统计转移类型
  unique_lu_transitions <- unique(lu_transitions)
  cat("\n发现的转移类型:\n")
  lu_counts <- table(lu_transitions)
  print(lu_counts)
  
  # 创建转移变量矩阵
  cat("\n创建转移变量矩阵:\n")
  lu_transition_vars <- data.frame(row.names = data$ProfileID)
  
  for(transition in unique_lu_transitions) {
    clean_name <- gsub("→", "_to_", transition)
    clean_name <- paste0("LU_", clean_name)
    lu_transition_vars[[clean_name]] <- as.numeric(lu_transitions == transition)
  }
  
  cat("土地利用转移变量矩阵:\n")
  print(lu_transition_vars)
  
  # 验证
  cat("\n验证：每行求和应该等于1\n")
  row_sums <- rowSums(lu_transition_vars)
  print(row_sums)
  
  # 2. 土壤类型转移
  cat("\n🌱 土壤类型转移分析:\n")
  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`
  
  # 创建转移标识
  st_transitions <- paste(soiltype_1980, "→", soiltype_2023, sep = "")
  cat("每个样本的转移类型:\n")
  for(i in 1:length(st_transitions)) {
    cat("  ", data$ProfileID[i], ":", st_transitions[i], "\n")
  }
  
  # 统计转移类型
  unique_st_transitions <- unique(st_transitions)
  cat("\n发现的转移类型:\n")
  st_counts <- table(st_transitions)
  print(st_counts)
  
  # 创建转移变量矩阵
  cat("\n创建转移变量矩阵:\n")
  st_transition_vars <- data.frame(row.names = data$ProfileID)
  
  for(transition in unique_st_transitions) {
    clean_name <- gsub("→", "_to_", transition)
    clean_name <- paste0("ST_", clean_name)
    st_transition_vars[[clean_name]] <- as.numeric(st_transitions == transition)
  }
  
  cat("土壤类型转移变量矩阵:\n")
  print(st_transition_vars)
  
  # 3. 合并所有转移变量
  cat("\n📊 最终转移变量矩阵:\n")
  all_transition_vars <- cbind(lu_transition_vars, st_transition_vars)
  print(all_transition_vars)
  
  cat("\n矩阵维度:", nrow(all_transition_vars), "行 ×", ncol(all_transition_vars), "列\n")
  cat("这个矩阵可以直接作为RDA模型的协变量输入！\n")
  
  return(all_transition_vars)
}

# 运行演示
transition_vars <- create_transition_demo(demo_data)

cat("\n=== RDA模型输入格式演示 ===\n")
cat("假设您还有环境变量（温度、降水等）:\n")

# 模拟环境变量
env_vars <- data.frame(
  Temperature = rnorm(10, 15, 3),
  Precipitation = rnorm(10, 500, 100),
  Elevation = rnorm(10, 200, 50),
  row.names = demo_data$ProfileID
)

cat("环境变量矩阵:\n")
print(round(env_vars, 2))

# 合并协变量
cat("\n最终协变量矩阵（环境变量 + 转移变量）:\n")
final_covariates <- cbind(env_vars, transition_vars)
print(round(final_covariates, 2))

cat("\n📋 RDA模型调用示例:\n")
cat("# 假设响应变量矩阵为 soil_changes (△pH, △SOM, △TN, △TP, △物理粘粒)\n")
cat("# 协变量矩阵为 final_covariates\n")
cat("rda_result <- rda(soil_changes ~ ., data = final_covariates)\n")
cat("\n这样就能分析转移过程对土壤变化的影响了！\n")

cat("\n=== 转移变量的优势 ===\n")
cat("1. 直接反映变化过程：农田→林地 vs 农田→农田\n")
cat("2. 数值形式：0/1变量，可直接输入模型\n")
cat("3. 维度合理：样本数 × 实际转移类型数\n")
cat("4. 解释清晰：每个系数代表特定转移的影响\n")
cat("5. 突出变化：符合您研究变化过程的目标\n")
