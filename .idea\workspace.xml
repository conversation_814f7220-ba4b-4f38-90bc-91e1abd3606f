<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ca70f05a-2605-4425-bf05-9a2ddba033ab" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.augment/rules/RULES.md" beforeDir="false" afterPath="$PROJECT_DIR$/.augment/rules/RULES.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cunzhi-memory/metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/.cunzhi-memory/metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cunzhi-memory/patterns.md" beforeDir="false" afterPath="$PROJECT_DIR$/.cunzhi-memory/patterns.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cunzhi-memory/rules.md" beforeDir="false" afterPath="$PROJECT_DIR$/.cunzhi-memory/rules.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.kiro/steering/analysis-workflow.md" beforeDir="false" afterPath="$PROJECT_DIR$/.kiro/steering/analysis-workflow.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/03 VIF方差膨胀剔除/3D变量中文化解决方案.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis.py" beforeDir="false" afterPath="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/03 VIF方差膨胀剔除/第三章中文变量名优化说明.md" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zLbjZ5fuPlHfbbuQFdfFhAn2vW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python 测试.Python 测试 (test_letter_logic.py 内).executor": "Run",
    "Python 测试.test_assumptions_example.test_real_data_example 的 Python 测试.executor": "Run",
    "Python.08_assumption_tests.executor": "Run",
    "Python.A_main.executor": "Run",
    "Python.B_config.executor": "Run",
    "Python.a_assumption_main.executor": "Run",
    "Python.ajk.executor": "Run",
    "Python.assumption_tests.executor": "Run",
    "Python.config.executor": "Run",
    "Python.correlation_analysis.executor": "Run",
    "Python.main.executor": "Run",
    "Python.main_simple.executor": "Run",
    "Python.statistical_analysis.executor": "Run",
    "Python.tif提取-论文.executor": "Run",
    "Python.vif_analysis (1).executor": "Run",
    "Python.vif_analysis copy.executor": "Run",
    "Python.vif_analysis.executor": "Run",
    "Python.土壤剖面数据标准化.executor": "Run",
    "Python.批量重命名工具.executor": "Run",
    "Python.数据统计.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "E:/05 Python/Devway/01 硕士论文",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\05 Python\Devway\01 硕士论文\05 时空变化分析" />
    </key>
  </component>
  <component name="RunManager" selected="Python.01_main_analysis">
    <configuration name="01_main_analysis" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/07 Python_RDA分析" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/07 Python_RDA分析/01_main_analysis.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="correlation_analysis" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/03 VIF方差膨胀剔除/correlation_analysis.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run_analysis" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/07 Python_RDA分析" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/07 Python_RDA分析/run_analysis.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="vif_analysis copy" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/03 VIF方差膨胀剔除/vif_analysis copy.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="vif_analysis" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="01 硕士论文" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/03 VIF方差膨胀剔除" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/03 VIF方差膨胀剔除/vif_analysis.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.01_main_analysis" />
        <item itemvalue="Python.run_analysis" />
        <item itemvalue="Python.vif_analysis copy" />
        <item itemvalue="Python.vif_analysis" />
        <item itemvalue="Python.correlation_analysis" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ca70f05a-2605-4425-bf05-9a2ddba033ab" name="更改" comment="" />
      <created>1751513435438</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751513435438</updated>
    </task>
    <servers />
  </component>
</project>