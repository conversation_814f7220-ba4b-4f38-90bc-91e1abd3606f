#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行Python RDA分析的主脚本
作者: AI Assistant
日期: 2025-01-29
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'numpy', 'pandas', 'matplotlib', 'seaborn', 
        'scikit-learn', 'scipy', 'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下包，请安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def run_python_analysis():
    """运行Python RDA分析"""
    print("\n🐍 === 开始Python RDA分析 ===")
    
    try:
        from main_analysis import main as run_rda
        results = run_rda()
        
        if results is not None:
            print("✅ Python RDA分析成功完成")
            return True
        else:
            print("❌ Python RDA分析失败")
            return False
            
    except Exception as e:
        print(f"❌ Python分析出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comparison():
    """运行对比分析"""
    print("\n🔄 === 开始对比分析 ===")
    
    try:
        from comparison_analysis import main as run_comparison
        results = run_comparison()
        
        if results is not None:
            print("✅ 对比分析成功完成")
            return True
        else:
            print("❌ 对比分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 对比分析出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 === Python RDA分析系统 ===")
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 运行Python分析
    python_success = run_python_analysis()
    
    if python_success:
        # 运行对比分析
        comparison_success = run_comparison()
        
        if comparison_success:
            print("\n🎉 === 所有分析完成 ===")
            print("📁 结果文件夹:")
            print("   - 07 Python_RDA分析/ (Python分析结果)")
            print("   - 08 RDA对比分析/ (R vs Python对比)")
            return True
    
    print("\n❌ 分析未完全成功")
    return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✨ 分析完成！您现在可以:")
        print("1. 查看Python版RDA双序图")
        print("2. 对比R和Python的分析结果")
        print("3. 阅读详细的对比报告")
    else:
        print("\n💡 如果遇到问题，请检查:")
        print("1. 数据文件路径是否正确")
        print("2. 所有依赖包是否已安装")
        print("3. Python版本是否兼容 (推荐3.7+)")
