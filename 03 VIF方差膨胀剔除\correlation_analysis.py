# -*- coding: utf-8 -*-
"""
相关性分析脚本 - 基于VIF筛选结果的优化版本

本脚本在VIF筛选出的变量基础上，为每个土壤属性执行相关性分析，并生成中文化报告。

主要功能：
1. 自动读取VIF筛选结果和中文变量名映射
2. 识别2D/3D辅助变量并分别分析
3. 为各土壤属性计算相关性系数
4. 生成中文化热图和Excel报告

优化特点：
- 自动从VIF结果中获取中文变量名，无需重复定义
- 热图显示中文变量名，提升可读性
- 保持与第四章建模的兼容性
"""

import pandas as pd
import os
from scipy.stats import pearsonr, spearmanr, kendalltau
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import numpy as np

# --- 全局配置 ---
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*constant.*')

# =============================================================================
# --- 1. 用户配置 ---
# =============================================================================

# -- 文件路径 --
vif_result_filepath = '../00 原始数据/嫩江_VIF_筛选结果/VIF_筛选结果.xlsx'
base_output_dir = '../00 原始数据/嫩江_相关性分析结果'

# -- 功能开关 --
# 全局控制是否生成热图
generate_heatmaps = True

# -- 热图颜色条配置 --
colorbar_shrink = 1
colorbar_width = 0.4



# -- 数据筛选 --
# 县城过滤，空列表不过滤
county_filter = ['嫩江', '梨树', '铁岭', '科左','凤城']
county_filter_column = 'City'
# 年份过滤，空列表使用全部年份
year_filter = [1980, 2023]

# -- 核心变量 --
# 因变量 (土壤属性)
target_properties = [
    'pH', 'SOM', 'TN', 'TP', '物理粘粒'
]
# 三维变量定义 (剖面层次变量)
three_d_vars = [
   'bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'
]
# 无需计算的元数据列
utility_cols = [
    'ProfileID', 'Longitude', 'Latitude', 'City', 'location', 
    'Soilclass', 'LandUse', 'year', '深度范围', '深度中点']

# -- 分析参数 --
# P值显著性阈值
p_value_threshold = 0.05
# 新增：选择相关性分析方法: 'pearson', 'spearman', 'kendall'
correlation_method = 'spearman'  # 默认使用Spearman，因为它更具普适性

# =============================================================================
# --- 2. 核心函数 ---
# =============================================================================

def calc_corr(dataframe, target_col, predictor_cols, method='pearson'):
    """为单个目标属性计算与所有预测变量的相关性。"""
    results = []
    print(f"  -> 计算 '{target_col}' 的相关性 (方法: {method})...")
    
    for predictor_col in predictor_cols:
        # 对每个预测变量，最大化利用样本进行成对相关性计算
        paired_df = dataframe[[target_col, predictor_col]].dropna()

        if len(paired_df) < 3:
            print(f"    - 警告: '{target_col}' 与 '{predictor_col}' 有效样本对不足，跳过。")
            continue

        # 检查变量是否为常量
        if paired_df[predictor_col].std() == 0:
            print(f"    - 警告: 预测变量 '{predictor_col}' 是常量，跳过。")
            continue
        if paired_df[target_col].std() == 0:
            print(f"    - 警告: 目标变量 '{target_col}' 是常量，跳过。")
            continue

        try:
            if method == 'pearson':
                r, p = pearsonr(paired_df[target_col], paired_df[predictor_col])
            elif method == 'spearman':
                r, p = spearmanr(paired_df[target_col], paired_df[predictor_col])
            elif method == 'kendall':
                r, p = kendalltau(paired_df[target_col], paired_df[predictor_col])
            else:
                raise ValueError(f"错误：无效的相关性分析方法 '{method}'")
        except Exception as e:
            print(f"    - 错误: 计算 '{target_col}' 与 '{predictor_col}' 相关性时出错: {e}")
            continue

        results.append({
            '辅助变量': predictor_col,
            '相关系数 (r)': r,
            'P值': p,
            '是否显著 (p<0.05)': '是' if p < p_value_threshold else '否',
            '样本数': len(paired_df)
        })
        
    if not results:
        return pd.DataFrame()
        
    results_df = pd.DataFrame(results)
    results_df['r_abs'] = results_df['相关系数 (r)'].abs()
    return results_df.sort_values(by='r_abs', ascending=False).drop(columns='r_abs')


def load_data(filepath):
    """加载并返回Excel数据。"""
    try:
        df = pd.read_excel(filepath)
        print(f"成功加载数据: {df.shape[0]} 行, {df.shape[1]} 列。")
        return df
    except FileNotFoundError:
        print(f"错误: 输入文件未找到 '{filepath}'")
        return None
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def load_vif_variable_mapping(filepath):
    """从VIF结果文件中加载变量名映射（英文->中文），并添加3D变量映射"""
    # 3D变量的中文名映射（这些变量不经过VIF筛选）
    three_d_mapping = {
        'bdod': '土壤容重',
        'cec': '阳离子交换量',
        'Soil_Temperature_Mean': '土壤温度',
        'Soil_Water_Content_Mean': '土壤含水量',
        # 土壤属性（因变量）
        'pH': 'pH值',
        'SOM': '有机质',
        'TN': '全氮',
        'TP': '全磷',
        '物理粘粒': '粘粒含量'
    }

    try:
        vif_df = pd.read_excel(filepath, sheet_name='最终VIF值汇总')
        if '变量名(英文)' in vif_df.columns and '变量名(中文)' in vif_df.columns:
            # 从VIF结果中提取2D变量名映射
            vif_mapping = dict(zip(vif_df['变量名(英文)'], vif_df['变量名(中文)']))
            print(f"从VIF结果中加载了 {len(vif_mapping)} 个2D变量名映射")

            # 合并2D和3D变量映射
            combined_mapping = {**vif_mapping, **three_d_mapping}
            print(f"添加了 {len(three_d_mapping)} 个3D变量和土壤属性映射")
            print(f"总计 {len(combined_mapping)} 个变量名映射")
            return combined_mapping
        else:
            print("VIF结果文件中未找到中文变量名，仅使用3D变量映射")
            return three_d_mapping
    except Exception as e:
        print(f"加载VIF变量映射失败: {e}，仅使用3D变量映射")
        return three_d_mapping

def analyze_dim(data, target_properties, predictors, method, dimension_tag, aggregate_by=None):
    """根据维度执行相关性分析（2D聚合或3D分层）。"""
    reports = {}
    if not predictors:
        print(f"  -> 未发现{dimension_tag}变量，跳过分析。")
        return reports

    df_for_analysis = data
    if aggregate_by:
        # 确保聚合键存在于数据中
        if not all(col in data.columns for col in aggregate_by):
            print(f"  -> 警告: 聚合键 {aggregate_by} 不完全存在于数据中，跳过聚合。")
        else:
            # 对数值列取均值进行聚合
            numeric_cols = data.select_dtypes(include=np.number).columns.tolist()
            # 聚合字典，排除掉聚合键本身
            agg_dict = {col: 'mean' for col in numeric_cols if col not in aggregate_by}
            df_for_analysis = data.groupby(aggregate_by, as_index=False).agg(agg_dict)
            print(f"  -> 数据已按 {aggregate_by} 聚合至 {len(df_for_analysis)} 行，用于{dimension_tag}分析。")

    for prop in target_properties:
        if prop not in df_for_analysis.columns:
            continue
        report_df = calc_corr(df_for_analysis, prop, predictors, method=method)
        if not report_df.empty:
            report_df['变量维度'] = dimension_tag
            reports[prop] = report_df
            
    return reports

def merge_reports(reports_2d, reports_3d):
    """合并2D和3D报告，并按相关性统一排序。"""
    final_reports = {}
    all_props = set(reports_2d.keys()) | set(reports_3d.keys())

    for prop in all_props:
        df_2d = reports_2d.get(prop, pd.DataFrame())
        df_3d = reports_3d.get(prop, pd.DataFrame())
        
        combined_df = pd.concat([df_2d, df_3d], ignore_index=True)
        
        if not combined_df.empty:
            combined_df['r_abs'] = combined_df['相关系数 (r)'].abs()
            final_reports[prop] = combined_df.sort_values(by='r_abs', ascending=False).drop(columns='r_abs')
            
    return final_reports

def save_results(final_reports, predictors_2d, predictors_3d, base_output_dir, county, year):
    """保存Excel报告和所有热图。"""
    # 创建输出目录
    output_dir = os.path.join(base_output_dir, county, str(year))
    os.makedirs(output_dir, exist_ok=True)
    
    # --- 保存Excel ---
    excel_path = os.path.join(output_dir, '相关性分析报告.xlsx')
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 严格按照 target_properties 的顺序写入工作表
            for prop_name in target_properties:
                if prop_name in final_reports:
                    report_df = final_reports[prop_name]
                    safe_sheet_name = prop_name.replace('[', '').replace(']', '').replace('*', '')[:30]
                    report_df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
        print(f"\n  -> 已写入Excel报告: {excel_path}")
    except Exception as e:
        print(f"  -> 写入Excel失败: {e}")

    # --- 保存热图 ---
    if not generate_heatmaps:
        return
        
    heatmaps_path = os.path.join(output_dir, 'heatmaps')
    os.makedirs(heatmaps_path, exist_ok=True)
    
    all_vars = sorted({var for report in final_reports.values() for var in report['辅助变量']})
    matrix = pd.DataFrame(index=target_properties, columns=all_vars, dtype=float)
    for prop, report in final_reports.items():
        if prop in matrix.index:
            report_dict = report.set_index('辅助变量')['相关系数 (r)'].to_dict()
            for var, r_val in report_dict.items():
                if var in matrix.columns:
                    matrix.loc[prop, var] = r_val
                    
    matrix.dropna(axis=0, how='all', inplace=True)
    matrix.dropna(axis=1, how='all', inplace=True)

    # 生成合并的2D+3D热图
    vars_2d_in_matrix = [v for v in matrix.columns if v in predictors_2d]
    vars_3d_in_matrix = [v for v in matrix.columns if v in predictors_3d]

    # 合并2D和3D变量到一个热图中
    if vars_2d_in_matrix or vars_3d_in_matrix:
        # 按类型重新排列变量顺序：先2D，后3D
        combined_vars = vars_2d_in_matrix + vars_3d_in_matrix
        if combined_vars:
            combined_matrix = matrix[combined_vars].dropna(axis=0, how='all')
            plot_combined_heatmap(combined_matrix, f"{county}-{year} 辅助变量相关性图",
                                'heatmap_combined.png', variable_mapping,
                                vars_2d_in_matrix, vars_3d_in_matrix, heatmaps_path)


def plot_combined_heatmap(data, title, filename, var_mapping, vars_2d, vars_3d, heatmaps_path):
    """绘制合并的2D+3D热图，优化排版和字体"""
    if data.empty:
        return

    # 转置数据矩阵，使辅助变量在纵轴，土壤属性在横轴
    data_transposed = data.T

    # 应用中文变量名
    if var_mapping:
        data_transposed.index = [var_mapping.get(var, var) for var in data_transposed.index]
        data_transposed.columns = [var_mapping.get(var, var) for var in data_transposed.columns]

    # 创建反向映射：中文名 -> 英文名
    reverse_mapping = {v: k for k, v in var_mapping.items()} if var_mapping else {}

    # 分类变量并保持原有标签
    vars_2d_indices = []
    vars_3d_indices = []

    for i, var in enumerate(data_transposed.index):
        original_var = reverse_mapping.get(var, var)
        if original_var in vars_2d:
            vars_2d_indices.append(i)
        elif original_var in vars_3d:
            vars_3d_indices.append(i)

    # 根据变量数量动态调整尺寸 - 优化单元格比例
    n_soil_props = len(data_transposed.columns)
    n_aux_vars = len(data_transposed.index)

    # 字体设置 - 中文黑体，英文Times New Roman
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Times New Roman']
    plt.rcParams['font.serif'] = ['Times New Roman'] 
    
    title_fontsize = 16
    label_fontsize = 14
    tick_fontsize = 12
    ytick_fontsize = 12
    annot_fontsize = 10

    # 优化图形尺寸 - 无需右侧分组标识，最大化热图空间
    cell_width = 1.1   
    cell_height = 0.75 
    width = max(10, n_soil_props * cell_width + 1.2)  # 最小右侧空间
    height = max(8, n_aux_vars * cell_height + 1.5)   

    # 设置白色背景
    fig = plt.figure(figsize=(width, height), facecolor='white', edgecolor='none')
    fig.patch.set_facecolor('white')
    fig.patch.set_edgecolor('none')

    # 调整主热图区域布局 - 最大化利用空间，无需右侧预留
    ax = plt.axes([0.08, 0.12, 0.90, 0.82])  # 进一步扩大热图到90%宽度
    ax.set_facecolor('white')

    # 创建热图数据 - 使用coolwarm配色
    import numpy as np
    im = ax.imshow(data_transposed.values, cmap='coolwarm', aspect='auto',
                   vmin=-1, vmax=1, interpolation='nearest')

    # 添加数值标注 - 调大字体到14号
    for i in range(len(data_transposed.index)):
        for j in range(len(data_transposed.columns)):
            value = data_transposed.iloc[i, j]
            text_color = 'white' if abs(value) > 0.5 else 'black'
            ax.text(j, i, f'{value:.2f}', ha='center', va='center',
                   fontsize=14, color=text_color, weight='normal',  # 从12增加到14
                   fontfamily='Times New Roman')

    # X轴标签在顶部，45度旋转
    ax.xaxis.tick_top()
    ax.xaxis.set_label_position('top')

    # 设置刻度和标签
    ax.set_xticks(range(len(data_transposed.columns)))
    ax.set_yticks(range(len(data_transposed.index)))
    ax.set_xticklabels(data_transposed.columns, fontsize=tick_fontsize, rotation=45, ha='left',
                      fontfamily='SimHei')
    ax.set_yticklabels(data_transposed.index, fontsize=ytick_fontsize, fontfamily='SimHei')

    # 设置边界
    ax.set_xlim(-0.5, len(data_transposed.columns) - 0.5)
    ax.set_ylim(-0.5, len(data_transposed.index) - 0.5)

    # 添加网格线
    for i in range(len(data_transposed.columns) + 1):
        ax.axvline(i - 0.5, color='white', linewidth=0.5)
    for i in range(len(data_transposed.index) + 1):
        ax.axhline(i - 0.5, color='white', linewidth=0.5)

    # 去掉坐标轴的边框
    for spine in ax.spines.values():
        spine.set_visible(False)

    # 底部颜色条 - 与热图完全对齐
    cbar_ax = fig.add_axes([0.08, 0.05, 0.90, 0.04])  # 与热图宽度完全一致
    cbar = fig.colorbar(im, cax=cbar_ax, orientation='horizontal')
    cbar.set_ticks(np.arange(-1.0, 1.1, 0.2))
    cbar.set_label('相关系数 (r)', fontsize=label_fontsize, fontfamily='SimHei')
    cbar.ax.tick_params(labelsize=tick_fontsize)
    cbar.outline.set_visible(False)

    # 移除右侧分组标识 - 保持热图简洁美观
    
    # 移除左侧"辅助变量"标签

    # 移除标题，避免与顶部标签冲突

    # 保存图片
    plt.savefig(os.path.join(heatmaps_path, filename), dpi=500, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    print(f"  -> 已保存合并热图: {filename}")


def process_data_chunk(data_subset, county, year):
    """处理单个县城特定年份的数据，执行完整分析流程。"""
    print(f"\n--- 县城: {county}, 年份: {year} ---")
    if data_subset.empty:
        print("  -> 数据为空，跳过。")
        return

    # --- 1. 变量分类 ---
    all_predictors = [col for col in data_subset.columns if col not in set(target_properties + utility_cols)]
    predictors_3d = [col for col in all_predictors if col in three_d_vars]
    predictors_2d = [col for col in all_predictors if col not in three_d_vars]
    print(f"  -> 识别到 {len(predictors_2d)} 个2D变量和 {len(predictors_3d)} 个3D变量。")

    # --- 2. 分维度执行分析 ---
    print("\n  --- 开始分析: 3D变量 (分层尺度) ---")
    reports_3d = analyze_dim(data_subset, target_properties, predictors_3d, correlation_method, "3D")
    
    print("\n  --- 开始分析: 2D变量 (剖面尺度) ---")
    reports_2d = analyze_dim(data_subset, target_properties, predictors_2d, correlation_method, "2D", aggregate_by=['ProfileID', 'year'])

    # --- 3. 合并与排序 ---
    final_reports = merge_reports(reports_2d, reports_3d)
    if not final_reports:
        print("\n  -> 未生成任何有效报告，跳过输出。")
        return

    # --- 4. 保存结果 ---
    save_results(final_reports, predictors_2d, predictors_3d, base_output_dir, county, year)


def main():
    """主函数：加载数据、循环并调度分析。"""
    print("相关性分析脚本启动...")

    # 加载VIF筛选后的数据
    full_df = load_data(vif_result_filepath)
    if full_df is None:
        return

    # 加载变量名映射（从VIF结果中获取中文变量名）
    global variable_mapping
    variable_mapping = load_vif_variable_mapping(vif_result_filepath)

    # --- 按年份和县城迭代分析 ---
    years_to_process = year_filter if year_filter else sorted(full_df['year'].dropna().unique())
    print(f"\n待分析年份: {years_to_process}")

    for year in years_to_process:
        print(f"\n===== 处理年份: {year} =====")
        year_df = full_df[full_df['year'] == year]

        counties_to_process = county_filter if county_filter else sorted(year_df[county_filter_column].dropna().unique())
        
        for county in counties_to_process:
            data_subset = year_df[year_df[county_filter_column] == county]
            process_data_chunk(data_subset, county, year)

if __name__ == "__main__":
    main()