# -*- coding: utf-8 -*-
"""
VIF剔除过程的流程图可视化
展示关键节点和变量剔除流程
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from statsmodels.stats.outliers_influence import variance_inflation_factor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_vif_flowchart():
    """创建VIF剔除流程图"""
    
    # 读取数据并进行VIF分析（简化版）
    merged_file_path = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\辅助变量-两期数据.xlsx"
    output_dir = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_VIF_筛选结果"
    
    # 模拟关键节点数据
    key_stages = [
        {"stage": "开始", "variables": 46, "max_vif": 59064536.63, "removed": ""},
        {"stage": "高VIF剔除", "variables": 40, "max_vif": 10999.97, "removed": "降水量、气温、NDVI等6个变量"},
        {"stage": "中等VIF处理", "variables": 30, "max_vif": 417.30, "removed": "净初级生产力、修正水体指数等10个变量"},
        {"stage": "低VIF优化", "variables": 20, "max_vif": 89.20, "removed": "光合有效辐射、地形湿润指数等10个变量"},
        {"stage": "最终筛选", "variables": 12, "max_vif": 3.41, "removed": "比值植被指数、谷深等8个变量"},
        {"stage": "完成", "variables": 12, "max_vif": 3.41, "removed": ""}
    ]
    
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    
    # 绘制流程图
    y_positions = np.linspace(0.8, 0.1, len(key_stages))
    x_center = 0.5
    
    for i, stage in enumerate(key_stages):
        y_pos = y_positions[i]
        
        # 绘制节点框
        if i == 0 or i == len(key_stages)-1:
            # 开始和结束节点 - 圆形
            circle = plt.Circle((x_center, y_pos), 0.08, 
                              facecolor='#2E86AB', edgecolor='white', linewidth=3)
            ax.add_patch(circle)
            color = 'white'
        else:
            # 处理节点 - 矩形
            rect = plt.Rectangle((x_center-0.15, y_pos-0.06), 0.3, 0.12, 
                               facecolor='#E8F4FD', edgecolor='#2E86AB', linewidth=2)
            ax.add_patch(rect)
            color = '#2E86AB'
        
        # 添加节点文字
        ax.text(x_center, y_pos, stage['stage'], 
               ha='center', va='center', fontsize=14, fontweight='bold', color=color)
        
        # 添加详细信息
        if i > 0 and i < len(key_stages)-1:
            info_text = f"变量数: {stage['variables']}   最大VIF: {stage['max_vif']:.1f}"
            ax.text(x_center + 0.25, y_pos + 0.02, info_text, 
                   ha='left', va='center', fontsize=11, color='#333')
            
            if stage['removed']:
                ax.text(x_center + 0.25, y_pos - 0.02, f"剔除: {stage['removed']}", 
                       ha='left', va='center', fontsize=10, color='#E63946', style='italic')
        
        # 绘制箭头
        if i < len(key_stages) - 1:
            ax.annotate('', xy=(x_center, y_positions[i+1] + 0.08), 
                       xytext=(x_center, y_pos - 0.08),
                       arrowprops=dict(arrowstyle='->', lw=3, color='#666'))
    
    # 设置图表属性
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_title('VIF变量剔除流程图', fontsize=24, fontweight='bold', pad=30)
    ax.axis('off')
    
    # 添加说明文字
    ax.text(0.02, 0.95, 'VIF阈值 = 5', fontsize=14, 
           bbox=dict(boxstyle="round,pad=0.3", facecolor='#FFE6E6', edgecolor='#E63946'))
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/VIF剔除流程图.png", dpi=500, bbox_inches='tight')
    plt.show()
    print(f"流程图已保存")

if __name__ == "__main__":
    create_vif_flowchart()