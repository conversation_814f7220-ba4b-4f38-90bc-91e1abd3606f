# 快速测试修复后的代码
cat("=== 快速测试修复后的RDA代码 ===\n")

# 加载包
source("06 RDA冗余分析/02_load_packages.R")
source("06 RDA冗余分析/03_data_processing.R")

# 设置参数
vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
response_vars <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")

# 读取数据
cat("读取数据...\n")
vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
change_data <- read_excel(change_file_path)

# 数据预处理
cat("数据预处理...\n")
processed_result <- preprocess_data_with_transitions(vif_data, change_data, response_vars, create_transitions = TRUE)

cat("预处理完成！\n")
cat("数据维度:", nrow(processed_result$data), "×", ncol(processed_result$data), "\n")
cat("协变量矩阵维度:", nrow(processed_result$explanatory_matrix), "×", ncol(processed_result$explanatory_matrix), "\n")

# 准备RDA分析数据
cat("准备RDA分析数据...\n")
response_matrix <- processed_result$data[, response_vars, drop = FALSE]
response_matrix <- response_matrix[complete.cases(response_matrix), , drop = FALSE]

explanatory_matrix <- processed_result$explanatory_matrix[rownames(response_matrix), , drop = FALSE]

# 处理协变量缺失值
for(i in 1:ncol(explanatory_matrix)) {
  if(is.numeric(explanatory_matrix[, i])) {
    explanatory_matrix[is.na(explanatory_matrix[, i]), i] <- mean(explanatory_matrix[, i], na.rm = TRUE)
  }
}

cat("最终分析数据:\n")
cat("  响应变量:", nrow(response_matrix), "×", ncol(response_matrix), "\n")
cat("  协变量:", nrow(explanatory_matrix), "×", ncol(explanatory_matrix), "\n")

# 执行简单RDA分析
cat("执行RDA分析...\n")
if(nrow(response_matrix) > 0 && ncol(explanatory_matrix) > 0) {
  rda_result <- rda(response_matrix ~ ., data = explanatory_matrix)
  
  cat("✅ RDA分析成功！\n")
  cat("总解释度:", round(RsquareAdj(rda_result)$adj.r.squared * 100, 2), "%\n")
  
  # 显著性检验
  overall_test <- anova(rda_result, permutations = 99)  # 减少置换次数加快测试
  cat("整体显著性P值:", round(overall_test$`Pr(>F)`[1], 4), "\n")
  
  cat("✅ 代码修复成功！\n")
} else {
  cat("❌ 数据准备失败\n")
}

cat("=== 测试完成 ===\n")
