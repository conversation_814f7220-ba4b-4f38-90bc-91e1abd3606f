# =============================================================================
# 数据处理函数
# =============================================================================

# 配置验证函数
validate_config <- function() {
  cat("=== 配置验证 ===\n")
  
  if(!file.exists(vif_file_path)) {
    stop("❌ VIF筛选结果文件不存在: ", vif_file_path)
  } else {
    cat("✅ VIF筛选结果文件存在\n")
  }
  
  if(!file.exists(change_file_path)) {
    stop("❌ 变化量数据文件不存在: ", change_file_path)
  } else {
    cat("✅ 变化量数据文件存在\n")
  }
  
  if(missing_threshold < 0 || missing_threshold > 1) {
    stop("❌ 缺失值阈值必须在0-1之间")
  }
  
  cat("✅ 配置验证通过\n\n")
}

# 创建输出目录
create_output_dir <- function(dir_path) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE)
    cat("📁 创建输出目录:", dir_path, "\n")
  } else {
    cat("📁 输出目录已存在:", dir_path, "\n")
  }
}

# 众数函数
get_mode <- function(x) {
  x <- x[!is.na(x)]
  if(length(x) == 0) return(NA)
  ux <- unique(x)
  ux[which.max(tabulate(match(x, ux)))]
}

# 改进的缺失值处理函数
handle_missing_values <- function(data, threshold = 0.3, 
                                 numeric_method = "mean", 
                                 categorical_method = "mode") {
  cat("=== 缺失值处理 ===\n")
  
  original_rows <- nrow(data)
  
  # 计算每行的缺失值比例
  missing_per_row <- rowSums(is.na(data)) / ncol(data)
  rows_to_remove <- missing_per_row > threshold
  
  if(sum(rows_to_remove) > 0) {
    cat("🗑️ 删除", sum(rows_to_remove), "行缺失值比例超过", threshold*100, "%的数据\n")
    data <- data[!rows_to_remove, ]
  }
  
  # 分析剩余数据的缺失值情况
  missing_summary <- colSums(is.na(data))
  missing_cols <- missing_summary[missing_summary > 0]
  
  if(length(missing_cols) > 0) {
    cat("📊 剩余缺失值情况:\n")
    for(col_name in names(missing_cols)) {
      cat("   ", col_name, ":", missing_cols[col_name], "个缺失值\n")
    }
    
    # 处理数值型变量
    numeric_cols <- sapply(data, is.numeric)
    if(any(numeric_cols)) {
      numeric_data <- data[, numeric_cols, drop = FALSE]
      missing_numeric <- colSums(is.na(numeric_data))
      missing_numeric <- missing_numeric[missing_numeric > 0]
      
      if(length(missing_numeric) > 0) {
        cat("🔢 填补数值型变量:\n")
        for(col_name in names(missing_numeric)) {
          if(numeric_method == "mean") {
            fill_value <- mean(numeric_data[[col_name]], na.rm = TRUE)
          } else {
            fill_value <- median(numeric_data[[col_name]], na.rm = TRUE)
          }
          data[[col_name]][is.na(data[[col_name]])] <- fill_value
          cat("   ", col_name, ": 用", round(fill_value, 4), "填补\n")
        }
      }
    }
    
    # 处理分类型变量
    categorical_cols <- !numeric_cols
    if(any(categorical_cols)) {
      categorical_data <- data[, categorical_cols, drop = FALSE]
      missing_categorical <- colSums(is.na(categorical_data))
      missing_categorical <- missing_categorical[missing_categorical > 0]
      
      if(length(missing_categorical) > 0) {
        cat("📝 填补分类型变量:\n")
        for(col_name in names(missing_categorical)) {
          if(categorical_method == "mode") {
            fill_value <- get_mode(categorical_data[[col_name]])
            data[[col_name]][is.na(data[[col_name]])] <- fill_value
            cat("   ", col_name, ": 用'", fill_value, "'填补\n")
          }
        }
      }
    }
  } else {
    cat("✅ 没有缺失值\n")
  }
  
  final_rows <- nrow(data)
  cat("📊 数据行数变化:", original_rows, "→", final_rows, "\n\n")
  
  return(data)
}

# 创建土地利用转移变量（修正版）
create_landuse_transition_vars <- function(data) {
  cat("🏞️ 创建土地利用转移变量...\n")

  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`

  # 处理缺失值
  landuse_1980[is.na(landuse_1980)] <- "未知"
  landuse_2023[is.na(landuse_2023)] <- "未知"

  # 获取所有实际发生的转移类型
  actual_transitions <- paste(landuse_1980, "to", landuse_2023, sep = "_")
  unique_transitions <- unique(actual_transitions)

  cat("   实际发生的转移类型:\n")
  transition_counts <- table(actual_transitions)
  for(i in 1:length(transition_counts)) {
    cat("     ", names(transition_counts)[i], ":", transition_counts[i], "个样本\n")
  }

  # 创建转移变量矩阵
  transition_vars <- data.frame(row.names = rownames(data))

  for(transition in unique_transitions) {
    # 清理变量名
    clean_name <- gsub("[^A-Za-z0-9_]", "", transition)
    clean_name <- paste0("LU_", clean_name)

    # 创建0/1变量
    transition_vars[[clean_name]] <- as.numeric(actual_transitions == transition)
  }

  cat("   创建了", ncol(transition_vars), "个土地利用转移变量\n")

  # 显示转移矩阵
  cat("   土地利用转移矩阵:\n")
  transition_matrix <- table(landuse_1980, landuse_2023)
  print(transition_matrix)

  return(transition_vars)
}

# 创建土壤类型转移变量（修正版）
create_soiltype_transition_vars <- function(data) {
  cat("🌱 创建土壤类型转移变量...\n")

  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`

  # 处理缺失值
  soiltype_1980[is.na(soiltype_1980)] <- "未知"
  soiltype_2023[is.na(soiltype_2023)] <- "未知"

  # 获取所有实际发生的转移类型
  actual_transitions <- paste(soiltype_1980, "to", soiltype_2023, sep = "_")
  unique_transitions <- unique(actual_transitions)

  cat("   实际发生的转移类型:\n")
  transition_counts <- table(actual_transitions)
  for(i in 1:length(transition_counts)) {
    cat("     ", names(transition_counts)[i], ":", transition_counts[i], "个样本\n")
  }

  # 创建转移变量矩阵
  transition_vars <- data.frame(row.names = rownames(data))

  for(transition in unique_transitions) {
    # 清理变量名
    clean_name <- gsub("[^A-Za-z0-9_]", "", transition)
    clean_name <- paste0("ST_", clean_name)

    # 创建0/1变量
    transition_vars[[clean_name]] <- as.numeric(actual_transitions == transition)
  }

  cat("   创建了", ncol(transition_vars), "个土壤类型转移变量\n")

  # 显示转移矩阵
  cat("   土壤类型转移矩阵:\n")
  transition_matrix <- table(soiltype_1980, soiltype_2023)
  print(transition_matrix)

  return(transition_vars)
}

# 创建独热编码变量（学术标准方法）
create_dummy_variables <- function(data) {
  cat("🔢 创建独热编码变量（学术标准方法）...\n")

  dummy_vars <- data.frame(row.names = rownames(data))

  # 1. 土地利用类型独热编码
  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`
  landuse_1980[is.na(landuse_1980)] <- "未知"
  landuse_2023[is.na(landuse_2023)] <- "未知"

  # 1980年土地利用类型
  unique_lu_1980 <- unique(landuse_1980)
  for(lu_type in unique_lu_1980) {
    if(lu_type != "未知") {  # 排除未知类型作为参考类别
      var_name <- paste0("LU1980_", gsub("[^A-Za-z0-9]", "", lu_type))
      dummy_vars[[var_name]] <- as.numeric(landuse_1980 == lu_type)
    }
  }

  # 2023年土地利用类型
  unique_lu_2023 <- unique(landuse_2023)
  for(lu_type in unique_lu_2023) {
    if(lu_type != "未知") {  # 排除未知类型作为参考类别
      var_name <- paste0("LU2023_", gsub("[^A-Za-z0-9]", "", lu_type))
      dummy_vars[[var_name]] <- as.numeric(landuse_2023 == lu_type)
    }
  }

  # 2. 土壤类型独热编码
  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`
  soiltype_1980[is.na(soiltype_1980)] <- "未知"
  soiltype_2023[is.na(soiltype_2023)] <- "未知"

  # 1980年土壤类型
  unique_st_1980 <- unique(soiltype_1980)
  for(st_type in unique_st_1980) {
    if(st_type != "未知") {  # 排除未知类型作为参考类别
      var_name <- paste0("ST1980_", gsub("[^A-Za-z0-9]", "", st_type))
      dummy_vars[[var_name]] <- as.numeric(soiltype_1980 == st_type)
    }
  }

  # 2023年土壤类型
  unique_st_2023 <- unique(soiltype_2023)
  for(st_type in unique_st_2023) {
    if(st_type != "未知") {  # 排除未知类型作为参考类别
      var_name <- paste0("ST2023_", gsub("[^A-Za-z0-9]", "", st_type))
      dummy_vars[[var_name]] <- as.numeric(soiltype_2023 == st_type)
    }
  }

  cat("   创建了", ncol(dummy_vars), "个独热编码变量\n")
  cat("   1980年土地利用类型:", length(unique_lu_1980), "个\n")
  cat("   2023年土地利用类型:", length(unique_lu_2023), "个\n")
  cat("   1980年土壤类型:", length(unique_st_1980), "个\n")
  cat("   2023年土壤类型:", length(unique_st_2023), "个\n")

  return(dummy_vars)
}

# 创建综合变化指标
create_change_indicators <- function(data) {
  cat("📊 创建综合变化指标...\n")

  change_vars <- data.frame(row.names = rownames(data))

  # 1. 土地利用变化指标
  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`
  landuse_1980[is.na(landuse_1980)] <- "未知"
  landuse_2023[is.na(landuse_2023)] <- "未知"

  # 土地利用是否发生变化
  change_vars$LU_Changed <- as.numeric(landuse_1980 != landuse_2023)

  # 2. 土壤类型变化指标
  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`
  soiltype_1980[is.na(soiltype_1980)] <- "未知"
  soiltype_2023[is.na(soiltype_2023)] <- "未知"

  # 土壤类型是否发生变化
  change_vars$ST_Changed <- as.numeric(soiltype_1980 != soiltype_2023)

  # 3. 综合变化指标
  change_vars$Any_Change <- as.numeric(change_vars$LU_Changed == 1 | change_vars$ST_Changed == 1)

  # 4. 变化强度指标（基于土壤属性变化量的绝对值）
  if(all(c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒") %in% colnames(data))) {
    soil_changes <- data[, c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")]
    soil_changes <- soil_changes[complete.cases(soil_changes), ]

    if(nrow(soil_changes) > 0) {
      # 标准化后计算综合变化强度
      soil_changes_scaled <- scale(soil_changes)
      change_vars$Change_Intensity <- rowSums(abs(soil_changes_scaled), na.rm = TRUE)
    }
  }

  cat("   创建了", ncol(change_vars), "个综合变化指标\n")
  cat("   土地利用变化样本数:", sum(change_vars$LU_Changed, na.rm = TRUE), "\n")
  cat("   土壤类型变化样本数:", sum(change_vars$ST_Changed, na.rm = TRUE), "\n")
  cat("   任意变化样本数:", sum(change_vars$Any_Change, na.rm = TRUE), "\n")

  return(change_vars)
}

# 分析转移模式
analyze_transition_patterns <- function(data) {
  cat("🔍 分析转移模式...\n")

  # 土地利用转移分析
  landuse_1980 <- data$`LandUse-1980`
  landuse_2023 <- data$`LandUse-2023`
  landuse_1980[is.na(landuse_1980)] <- "未知"
  landuse_2023[is.na(landuse_2023)] <- "未知"

  cat("\n--- 土地利用转移分析 ---\n")
  lu_transition_matrix <- table(landuse_1980, landuse_2023)
  print(lu_transition_matrix)

  # 计算转移概率
  lu_transition_prob <- prop.table(lu_transition_matrix, margin = 1)
  cat("\n土地利用转移概率矩阵:\n")
  print(round(lu_transition_prob, 3))

  # 土壤类型转移分析
  soiltype_1980 <- data$`Soilclass-1980`
  soiltype_2023 <- data$`Soilclass-2023`
  soiltype_1980[is.na(soiltype_1980)] <- "未知"
  soiltype_2023[is.na(soiltype_2023)] <- "未知"

  cat("\n--- 土壤类型转移分析 ---\n")
  st_transition_matrix <- table(soiltype_1980, soiltype_2023)
  print(st_transition_matrix)

  # 计算转移概率
  st_transition_prob <- prop.table(st_transition_matrix, margin = 1)
  cat("\n土壤类型转移概率矩阵:\n")
  print(round(st_transition_prob, 3))

  # 识别主要转移类型
  cat("\n--- 主要转移类型识别 ---\n")

  # 土地利用主要转移
  lu_transitions <- paste(landuse_1980, "→", landuse_2023)
  lu_transition_counts <- sort(table(lu_transitions), decreasing = TRUE)
  cat("土地利用主要转移类型:\n")
  for(i in 1:min(5, length(lu_transition_counts))) {
    cat("  ", names(lu_transition_counts)[i], ":", lu_transition_counts[i], "个样本\n")
  }

  # 土壤类型主要转移
  st_transitions <- paste(soiltype_1980, "→", soiltype_2023)
  st_transition_counts <- sort(table(st_transitions), decreasing = TRUE)
  cat("\n土壤类型主要转移类型:\n")
  for(i in 1:min(5, length(st_transition_counts))) {
    cat("  ", names(st_transition_counts)[i], ":", st_transition_counts[i], "个样本\n")
  }

  return(list(
    lu_matrix = lu_transition_matrix,
    lu_prob = lu_transition_prob,
    st_matrix = st_transition_matrix,
    st_prob = st_transition_prob
  ))
}

# 变量分类函数
classify_variables <- function(env_vars) {
  cat("=== 变量分类 ===\n")
  
  # 基于VIF分析脚本的变量分组
  variable_groups <- list(
    climate = c('Air_Temperature_2m_Mean', 'LST', 'Total_Precipitation_Mean', 'Total_Precipitation_Sum',
                'Total_Evaporation_Mean', 'Surface_Net_Solar_Radiation_Mean', 'Surface_Net_Thermal_Radiation_Mean',
                'Wind_U_Component_10m_Mean', 'Wind_V_Component_10m_Mean'),
    terrain = c('250DEM', 'Aspect', 'Slope', 'Plan_Curvature', 'Profile_Curvature',
                'Terrain_Ruggedness_Index', 'Topographic_Position_Index', 'Topographic_Wetness_Index',
                'Valley_Depth', 'Vertical_Distance_to_Channel_Network', 'Flow_Accumulation',
                'Flow_Direction', 'Sink_Route'),
    vegetation = c('NDVI', 'EVI', 'NDWI', 'MNDWI', 'SAVI', 'RVI', 'LAI', 'FAPAR', 'FVC',
                   'NPP', 'GPP', 'BSI', 'IBI', 'Coloration_Index', 'Redness_Index', 'Saturation_Index'),
    human = c('Population', 'GDP', 'NTL', 'Built_Up', 'CLCD'),
    soil_3d = c('bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'),
    categorical = c('landform', 'lithology', 'soiltype')
  )
  
  # 静态协变量：地形 + 分类变量
  static_vars <- c(variable_groups$terrain, variable_groups$categorical)
  # 动态协变量：气候 + 植被 + 人类活动 + 3D土壤
  dynamic_vars <- c(variable_groups$climate, variable_groups$vegetation, 
                   variable_groups$human, variable_groups$soil_3d)
  
  static_available <- intersect(static_vars, env_vars)
  dynamic_available <- intersect(dynamic_vars, env_vars)
  unclassified <- setdiff(env_vars, c(static_available, dynamic_available))
  
  cat("📊 静态协变量(", length(static_available), "个):", paste(static_available, collapse = ", "), "\n")
  cat("📈 动态协变量(", length(dynamic_available), "个):", paste(dynamic_available, collapse = ", "), "\n")
  
  if(length(unclassified) > 0) {
    cat("❓ 未分类变量:", paste(unclassified, collapse = ", "), "\n")
    dynamic_available <- c(dynamic_available, unclassified)
  }
  
  return(list(
    static = static_available,
    dynamic = dynamic_available,
    groups = variable_groups
  ))
}

# 数据预处理主函数
preprocess_data <- function(vif_data, change_data, response_vars) {
  cat("=== 数据预处理 ===\n")
  
  # 基础信息列
  base_cols <- c("ProfileID", "Longitude", "Latitude", "City", "Location", 
                 "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
                 "深度范围", "深度中点")
  
  # VIF筛选后的环境变量
  env_cols <- setdiff(colnames(vif_data), c(base_cols, response_vars))
  
  # 构建最终数据集
  final_cols <- c(base_cols, response_vars, env_cols)
  available_cols <- intersect(final_cols, colnames(change_data))
  processed_data <- change_data[, available_cols]
  
  cat("📊 数据维度:", nrow(processed_data), "行 ×", ncol(processed_data), "列\n")
  cat("🎯 响应变量:", paste(response_vars, collapse = ", "), "\n")
  cat("🌍 环境变量数量:", length(env_cols), "\n")
  
  # 处理缺失值
  processed_data <- handle_missing_values(
    processed_data, 
    threshold = missing_threshold,
    numeric_method = numeric_missing_method,
    categorical_method = categorical_missing_method
  )
  
  return(list(
    data = processed_data,
    response_vars = response_vars,
    env_vars = env_cols,
    base_vars = base_cols
  ))
}

# 增强的数据预处理函数（包含转移变量）
preprocess_data_with_transitions <- function(vif_data, change_data, response_vars, create_transitions = TRUE) {
  cat("=== 增强数据预处理（含转移变量）===\n")

  # 基础信息列
  base_cols <- c("ProfileID", "Longitude", "Latitude", "City", "Location",
                 "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
                 "深度范围", "深度中点")

  # VIF筛选后的环境变量
  env_cols <- setdiff(colnames(vif_data), c(base_cols, response_vars))

  # 构建最终数据集
  final_cols <- c(base_cols, response_vars, env_cols)
  available_cols <- intersect(final_cols, colnames(change_data))
  processed_data <- change_data[, available_cols]

  cat("📊 数据维度:", nrow(processed_data), "行 ×", ncol(processed_data), "列\n")
  cat("🎯 响应变量:", paste(response_vars, collapse = ", "), "\n")
  cat("🌍 环境变量数量:", length(env_cols), "\n")

  # 处理缺失值
  processed_data <- handle_missing_values(
    processed_data,
    threshold = missing_threshold,
    numeric_method = numeric_missing_method,
    categorical_method = categorical_missing_method
  )

  # 准备环境变量矩阵
  explanatory_vars <- processed_data[, env_cols, drop = FALSE]

  # 创建分类变量编码（学术标准方法）
  transition_patterns <- NULL
  if(create_transitions) {
    cat("\n🔄 创建分类变量编码（学术标准方法）...\n")

    # 分析转移模式（用于报告）
    transition_patterns <- analyze_transition_patterns(processed_data)

    # 方法1：独热编码（主要方法，符合学术标准）
    dummy_vars <- create_dummy_variables(processed_data)

    # 方法2：综合变化指标（补充方法）
    change_indicators <- create_change_indicators(processed_data)

    # 方法3：仅保留主要转移类型（避免稀疏性）
    major_transitions <- NULL
    if(nrow(processed_data) > 50) {  # 样本量足够时才创建转移变量
      cat("   样本量充足，创建主要转移变量...\n")

      # 只保留样本数≥5的转移类型
      landuse_transitions <- create_landuse_transition_vars(processed_data)
      soiltype_transitions <- create_soiltype_transition_vars(processed_data)

      # 筛选主要转移
      lu_major <- landuse_transitions[, sapply(landuse_transitions, function(x) sum(x) >= 5), drop = FALSE]
      st_major <- soiltype_transitions[, sapply(soiltype_transitions, function(x) sum(x) >= 5), drop = FALSE]

      if(ncol(lu_major) > 0 || ncol(st_major) > 0) {
        major_transitions <- cbind(lu_major, st_major)
        cat("   保留", ncol(major_transitions), "个主要转移变量\n")
      }
    }

    # 合并所有变量
    all_categorical <- dummy_vars  # 以独热编码为主

    # 添加变化指标
    all_categorical <- cbind(all_categorical, change_indicators)

    # 添加主要转移变量（如果有）
    if(!is.null(major_transitions) && ncol(major_transitions) > 0) {
      all_categorical <- cbind(all_categorical, major_transitions)
    }

    # 移除常量列
    constant_cols <- sapply(all_categorical, function(x) length(unique(x)) <= 1)
    if(any(constant_cols)) {
      cat("   移除", sum(constant_cols), "个常量变量\n")
      all_categorical <- all_categorical[, !constant_cols, drop = FALSE]
    }

    # 合并到环境变量中
    explanatory_vars <- cbind(explanatory_vars, all_categorical)
    cat("   最终保留", ncol(all_categorical), "个分类相关变量\n")
    cat("   其中独热编码变量:", ncol(dummy_vars), "个\n")
    cat("   变化指标:", ncol(change_indicators), "个\n")
    if(!is.null(major_transitions)) {
      cat("   主要转移变量:", ncol(major_transitions), "个\n")
    }
  }

  return(list(
    data = processed_data,
    response_vars = response_vars,
    env_vars = colnames(explanatory_vars),
    base_vars = base_cols,
    explanatory_matrix = explanatory_vars,
    transition_patterns = transition_patterns
  ))
}
