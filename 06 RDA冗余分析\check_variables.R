# 检查变量和解释度
cat("=== 检查RDA分析中的变量问题 ===\n")

# 加载数据
source("06 RDA冗余分析/02_load_packages.R")
source("06 RDA冗余分析/03_data_processing.R")

vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
response_vars <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")

vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
change_data <- read_excel(change_file_path)

processed_result <- preprocess_data_with_transitions(vif_data, change_data, response_vars, create_transitions = TRUE)

cat("\n=== 变量统计 ===\n")
cat("总变量数:", ncol(processed_result$explanatory_matrix), "\n")
cat("环境变量数:", length(processed_result$env_vars), "\n")

# 检查第一个深度层的详细情况
depth_filter <- processed_result$data$深度中点 == 5
layer_data <- processed_result$data[depth_filter, ]
response_matrix <- layer_data[, response_vars, drop = FALSE]
response_matrix <- response_matrix[complete.cases(response_matrix), , drop = FALSE]

explanatory_matrix <- processed_result$explanatory_matrix[rownames(response_matrix), , drop = FALSE]

cat("\n=== 0-10cm深度层详细分析 ===\n")
cat("样本数:", nrow(response_matrix), "\n")
cat("协变量数:", ncol(explanatory_matrix), "\n")

# 检查常量列
constant_cols <- sapply(explanatory_matrix, function(x) length(unique(x[!is.na(x)])) <= 1)
cat("常量列数:", sum(constant_cols), "\n")
if(sum(constant_cols) > 0) {
  cat("常量列名:", paste(names(explanatory_matrix)[constant_cols], collapse = ", "), "\n")
}

# 移除常量列后重新分析
explanatory_clean <- explanatory_matrix[, !constant_cols, drop = FALSE]
cat("清理后协变量数:", ncol(explanatory_clean), "\n")

# 执行简单RDA看解释度
rda_simple <- rda(response_matrix ~ ., data = explanatory_clean)
explained_var <- RsquareAdj(rda_simple)$adj.r.squared * 100

cat("调整后解释度:", round(explained_var, 2), "%\n")

# 检查是否有太多变量相对于样本数
cat("\n=== 变量/样本比例检查 ===\n")
cat("变量数/样本数比例:", round(ncol(explanatory_clean)/nrow(response_matrix), 2), "\n")
cat("建议比例应该 < 0.1 (即样本数应该是变量数的10倍以上)\n")

if(ncol(explanatory_clean)/nrow(response_matrix) > 0.1) {
  cat("⚠️ 变量数相对于样本数过多，可能导致过拟合和低解释度\n")
  cat("建议：\n")
  cat("1. 进一步筛选变量（保留最重要的10-15个）\n")
  cat("2. 或者合并深度层增加样本数\n")
}

cat("\n=== 建议的改进方案 ===\n")
cat("1. 减少变量数：从", ncol(explanatory_clean), "个减少到15个以下\n")
cat("2. 使用主成分分析降维\n")
cat("3. 或者不分层，使用全部124个样本\n")
