# =============================================================================
# RDA图表测试脚本
# =============================================================================

# 清理环境
rm(list = ls())

# 加载必要的包
library(vegan)
library(readxl)
library(dplyr)

# 设置路径
vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
output_dir <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/RDA冗余分析"

# 创建输出目录
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
}

# 读取数据
cat("读取数据...\n")
vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
change_data <- read_excel(change_file_path)

# 数据预处理
cat("数据预处理...\n")
response_vars <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")
base_cols <- c("ProfileID", "Longitude", "Latitude", "City", "Location", 
               "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
               "深度范围", "深度中点")

# 获取环境变量
env_cols <- setdiff(colnames(vif_data), c(base_cols, response_vars))
final_cols <- c(base_cols, response_vars, env_cols)
available_cols <- intersect(final_cols, colnames(change_data))
processed_data <- change_data[, available_cols]

# 处理缺失值
processed_data <- processed_data[complete.cases(processed_data[, response_vars]), ]

# 准备RDA分析数据
response_matrix <- processed_data[, response_vars, drop = FALSE]
explanatory_vars <- processed_data[, env_cols, drop = FALSE]

# 移除常量列
constant_cols <- sapply(explanatory_vars, function(x) length(unique(x[!is.na(x)])) <= 1)
if(any(constant_cols)) {
  explanatory_vars <- explanatory_vars[, !constant_cols, drop = FALSE]
}

# 处理缺失值
explanatory_vars[] <- lapply(explanatory_vars, function(x) {
  if(is.numeric(x)) {
    x[is.na(x)] <- mean(x, na.rm = TRUE)
  } else {
    mode_val <- names(sort(table(x), decreasing = TRUE))[1]
    x[is.na(x)] <- mode_val
  }
  return(x)
})

cat("执行RDA分析...\n")
# 执行RDA
rda_result <- rda(response_matrix ~ ., data = explanatory_vars)

# 显著性检验
overall_test <- anova(rda_result, permutations = 999)
terms_test <- anova(rda_result, by = "terms", permutations = 999)

# 计算解释度
total_var <- sum(rda_result$CA$eig) + sum(rda_result$CCA$eig)
constrained_var <- sum(rda_result$CCA$eig)
explained_variance <- constrained_var / total_var * 100

# 计算各轴解释度
if(length(rda_result$CCA$eig) > 0) {
  axis_variance <- rda_result$CCA$eig / sum(rda_result$CCA$eig) * explained_variance
  names(axis_variance) <- paste0("RDA", 1:length(axis_variance))
} else {
  axis_variance <- numeric(0)
}

cat("总解释度:", round(explained_variance, 2), "%\n")
cat("RDA1解释度:", round(axis_variance[1], 2), "%\n")
cat("RDA2解释度:", round(axis_variance[2], 2), "%\n")

# 创建传统风格RDA双序图
cat("创建RDA双序图...\n")

create_traditional_rda_biplot <- function(rda_model, axis_var, explained_var, output_path) {
  
  # 提取坐标数据
  sites_scores <- scores(rda_model, display = "sites", choices = 1:2)
  species_scores <- scores(rda_model, display = "species", choices = 1:2)
  env_scores <- scores(rda_model, display = "bp", choices = 1:2)
  
  # 计算轴解释度
  axis1_var <- round(axis_var[1], 2)
  axis2_var <- round(axis_var[2], 2)
  
  # 创建图片
  png(output_path, width = 12, height = 10, units = "in", res = 300)
  
  # 设置图形参数
  par(mar = c(5, 5, 4, 2), family = "Arial")
  
  # 创建空白图
  plot(sites_scores, type = "n", 
       xlab = paste0("RDA1 (", axis1_var, "%)"),
       ylab = paste0("RDA2 (", axis2_var, "%)"),
       main = "RDA双序图",
       cex.lab = 1.4, cex.axis = 1.2, cex.main = 1.6,
       font.lab = 2, font.main = 2)
  
  # 添加网格线
  abline(h = 0, v = 0, col = "gray60", lty = 2, lwd = 1)
  
  # 添加样点（蓝色圆圈）
  points(sites_scores, pch = 21, bg = "lightblue", col = "blue", 
         cex = 1.2, lwd = 1.5)
  
  # 添加物种（响应变量，红色标签）
  text(species_scores, labels = rownames(species_scores), 
       col = "red", cex = 1.2, font = 2)
  
  # 添加环境变量箭头（红色箭头）
  arrows(0, 0, env_scores[,1] * 0.8, env_scores[,2] * 0.8,
         length = 0.1, col = "red", lwd = 2)
  
  # 添加环境变量标签（红色）
  text(env_scores * 0.9, labels = rownames(env_scores), 
       col = "red", cex = 1.0, font = 2)
  
  # 添加解释度信息
  legend("topright", 
         legend = paste("解释度:", round(explained_var, 2), "%"),
         bty = "n", cex = 1.2, text.font = 2)
  
  dev.off()
  
  cat("✅ RDA双序图已保存:", output_path, "\n")
}

# 生成图表
biplot_path <- file.path(output_dir, "RDA双序图_测试.png")
create_traditional_rda_biplot(rda_result, axis_variance, explained_variance, biplot_path)

# 输出结果摘要
cat("\n=== RDA分析结果摘要 ===\n")
cat("样本数:", nrow(response_matrix), "\n")
cat("响应变量数:", ncol(response_matrix), "\n")
cat("环境变量数:", ncol(explanatory_vars), "\n")
cat("总解释度:", round(explained_variance, 2), "%\n")
cat("整体显著性P值:", round(overall_test$`Pr(>F)`[1], 4), "\n")
cat("图表已保存至:", output_dir, "\n")

cat("\n🎉 测试完成！\n")
