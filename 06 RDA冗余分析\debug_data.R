# 调试数据结构
cat("=== 调试数据结构 ===\n")

# 加载包
library(readxl)
library(dplyr)

# 读取数据
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
change_data <- read_excel(change_file_path)

cat("数据维度:", nrow(change_data), "×", ncol(change_data), "\n")

cat("\n所有列名:\n")
print(colnames(change_data))

cat("\n检查深度相关列:\n")
depth_cols <- grep("深度", colnames(change_data), value = TRUE)
cat("深度相关列:", paste(depth_cols, collapse = ", "), "\n")

if("深度中点" %in% colnames(change_data)) {
  cat("\n深度中点列信息:\n")
  cat("数据类型:", class(change_data$深度中点), "\n")
  cat("唯一值:", paste(sort(unique(change_data$深度中点)), collapse = ", "), "\n")
  cat("缺失值数量:", sum(is.na(change_data$深度中点)), "\n")
  cat("前10个值:", paste(head(change_data$深度中点, 10), collapse = ", "), "\n")
} else {
  cat("\n❌ 没有找到'深度中点'列\n")
}

# 检查其他可能的深度列
if("深度范围" %in% colnames(change_data)) {
  cat("\n深度范围列信息:\n")
  cat("数据类型:", class(change_data$深度范围), "\n")
  cat("唯一值:", paste(unique(change_data$深度范围), collapse = ", "), "\n")
}

cat("\n=== 调试完成 ===\n")
