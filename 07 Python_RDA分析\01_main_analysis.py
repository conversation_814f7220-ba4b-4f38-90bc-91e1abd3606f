#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东北黑土区土壤剖面RDA冗余分析 - Python版本
作者: AI Assistant
日期: 2025-01-29

功能:
1. 数据加载和预处理
2. RDA冗余分析
3. 可视化和结果导出
4. 与R版本结果对比
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cross_decomposition import CCA
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class RDAAnalysis:
    """RDA冗余分析类"""
    
    def __init__(self, output_dir="Python_RDA结果"):
        self.output_dir = output_dir
        self.response_vars = ["△pH", "△SOM", "△TN", "△TP", "△物理粘粒"]
        self.results = {}
        
        # 创建输出目录
        import os
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 输出目录: {output_dir}")
    
    def load_data(self, vif_file, change_file):
        """加载数据"""
        print("\n📊 === 数据加载 ===")
        
        # 加载VIF筛选后的数据
        self.vif_data = pd.read_excel(vif_file, sheet_name="筛选后数据")
        print(f"✅ VIF数据: {self.vif_data.shape}")
        
        # 加载变化量数据
        self.change_data = pd.read_excel(change_file)
        print(f"✅ 变化量数据: {self.change_data.shape}")
        
        return self
    
    def preprocess_data(self):
        """数据预处理"""
        print("\n🔧 === 数据预处理 ===")
        
        # 合并数据
        self.merged_data = pd.merge(self.vif_data, self.change_data, on='ID', how='inner')
        print(f"📊 合并后数据: {self.merged_data.shape}")
        
        # 分离响应变量和环境变量
        self.Y = self.merged_data[self.response_vars].dropna()
        
        # 环境变量（排除ID、响应变量等）
        exclude_cols = ['ID', 'Depth_Mid', 'City', 'County'] + self.response_vars
        env_cols = [col for col in self.merged_data.columns if col not in exclude_cols]
        
        # 只保留数值型环境变量
        numeric_env_cols = []
        for col in env_cols:
            if self.merged_data[col].dtype in ['int64', 'float64']:
                numeric_env_cols.append(col)
        
        self.X = self.merged_data[numeric_env_cols].loc[self.Y.index]
        
        # 处理缺失值
        self.X = self.X.fillna(self.X.mean())
        
        print(f"🎯 响应变量: {self.Y.shape} - {list(self.Y.columns)}")
        print(f"🌍 环境变量: {self.X.shape} - 前5个: {list(self.X.columns[:5])}")
        
        return self
    
    def perform_rda(self):
        """执行RDA分析"""
        print("\n📈 === RDA分析 ===")
        
        # 标准化数据
        self.scaler_X = StandardScaler()
        self.scaler_Y = StandardScaler()
        
        X_scaled = self.scaler_X.fit_transform(self.X)
        Y_scaled = self.scaler_Y.fit_transform(self.Y)
        
        # 使用CCA进行RDA分析
        self.cca = CCA(n_components=min(5, min(X_scaled.shape[1], Y_scaled.shape[1])))
        X_c, Y_c = self.cca.fit_transform(X_scaled, Y_scaled)
        
        # 计算解释度
        self.explained_variance = self._calculate_explained_variance(X_scaled, Y_scaled, X_c, Y_c)
        
        # 保存结果
        self.results = {
            'X_scores': X_c,
            'Y_scores': Y_c,
            'X_loadings': self.cca.x_loadings_,
            'Y_loadings': self.cca.y_loadings_,
            'explained_variance': self.explained_variance,
            'sample_size': len(self.X)
        }
        
        print(f"✅ RDA分析完成")
        print(f"📊 样本数: {self.results['sample_size']}")
        print(f"📈 总解释度: {self.explained_variance:.2f}%")
        
        return self
    
    def _calculate_explained_variance(self, X, Y, X_c, Y_c):
        """计算解释度"""
        # 计算典型相关系数
        correlations = []
        for i in range(X_c.shape[1]):
            corr = np.corrcoef(X_c[:, i], Y_c[:, i])[0, 1]
            correlations.append(corr ** 2)
        
        # 总解释度
        total_variance = np.sum(correlations) / len(correlations) * 100
        return total_variance
    
    def create_biplot(self):
        """创建双序图"""
        print("\n🎨 === 创建双序图 ===")
        
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 样本点
        ax.scatter(self.results['X_scores'][:, 0], 
                  self.results['X_scores'][:, 1],
                  alpha=0.6, s=50, c='steelblue', label='样本点')
        
        # 响应变量箭头
        for i, var in enumerate(self.response_vars):
            if i < len(self.results['Y_loadings']):
                ax.arrow(0, 0, 
                        self.results['Y_loadings'][i, 0] * 3,
                        self.results['Y_loadings'][i, 1] * 3,
                        head_width=0.05, head_length=0.05, 
                        fc='red', ec='red', linewidth=2)
                ax.text(self.results['Y_loadings'][i, 0] * 3.5,
                       self.results['Y_loadings'][i, 1] * 3.5,
                       var, fontsize=12, fontweight='bold', color='red')
        
        # 环境变量箭头（只显示前10个最重要的）
        loadings_importance = np.sum(self.results['X_loadings'][:, :2] ** 2, axis=1)
        top_indices = np.argsort(loadings_importance)[-10:]
        
        for i in top_indices:
            ax.arrow(0, 0,
                    self.results['X_loadings'][i, 0] * 2,
                    self.results['X_loadings'][i, 1] * 2,
                    head_width=0.03, head_length=0.03,
                    fc='darkblue', ec='darkblue', alpha=0.7)
            ax.text(self.results['X_loadings'][i, 0] * 2.2,
                   self.results['X_loadings'][i, 1] * 2.2,
                   self.X.columns[i], fontsize=10, color='darkblue')
        
        # 设置图表
        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.7)
        ax.axvline(x=0, color='gray', linestyle='--', alpha=0.7)
        ax.set_xlabel('RDA1', fontsize=14, fontweight='bold')
        ax.set_ylabel('RDA2', fontsize=14, fontweight='bold')
        ax.set_title(f'RDA双序图 (解释度: {self.explained_variance:.1f}%)', 
                    fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 保存图表
        output_path = f"{self.output_dir}/RDA双序图_Python版.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 双序图已保存: {output_path}")
        return self
    
    def create_variable_importance(self):
        """创建变量重要性图"""
        print("\n📊 === 变量重要性分析 ===")
        
        # 计算变量重要性（基于载荷的平方和）
        importance = np.sum(self.results['X_loadings'][:, :2] ** 2, axis=1)
        
        # 创建DataFrame
        importance_df = pd.DataFrame({
            'Variable': self.X.columns,
            'Importance': importance
        }).sort_values('Importance', ascending=True).tail(15)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))
        
        bars = ax.barh(importance_df['Variable'], importance_df['Importance'], 
                      color='darkgreen', alpha=0.7)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.001, bar.get_y() + bar.get_height()/2, 
                   f'{width:.3f}', ha='left', va='center', fontsize=10)
        
        ax.set_xlabel('重要性得分', fontsize=14, fontweight='bold')
        ax.set_ylabel('环境变量', fontsize=14, fontweight='bold')
        ax.set_title('环境变量重要性排序 (Python版)', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3, axis='x')
        
        # 保存图表
        output_path = f"{self.output_dir}/变量重要性_Python版.png"
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 变量重要性图已保存: {output_path}")
        return importance_df
    
    def individual_response_analysis(self):
        """分响应变量分析"""
        print("\n🔬 === 分响应变量分析 ===")
        
        individual_results = {}
        
        for response_var in self.response_vars:
            print(f"📊 分析 {response_var}...")
            
            # 单响应变量数据
            y_single = self.Y[[response_var]].dropna()
            x_single = self.X.loc[y_single.index]
            
            # 标准化
            x_scaled = self.scaler_X.fit_transform(x_single)
            y_scaled = self.scaler_Y.fit_transform(y_single)
            
            # 单变量CCA
            cca_single = CCA(n_components=1)
            x_c, y_c = cca_single.fit_transform(x_scaled, y_scaled)
            
            # 计算变量重要性
            loadings = cca_single.x_loadings_[:, 0]
            importance = loadings ** 2
            
            # 保存结果
            var_importance = pd.DataFrame({
                'Variable': x_single.columns,
                'Importance': importance,
                'Loading': loadings
            }).sort_values('Importance', ascending=False).head(10)
            
            individual_results[response_var] = {
                'importance': var_importance,
                'x_scores': x_c,
                'y_scores': y_c,
                'loadings': cca_single.x_loadings_
            }
            
            # 创建单变量重要性图
            self._plot_single_variable_importance(var_importance, response_var)
        
        self.individual_results = individual_results
        return self
    
    def _plot_single_variable_importance(self, importance_df, response_var):
        """绘制单响应变量重要性图"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        bars = ax.bar(range(len(importance_df)), importance_df['Importance'], 
                     color='darkgreen', alpha=0.7)
        
        ax.set_xticks(range(len(importance_df)))
        ax.set_xticklabels(importance_df['Variable'], rotation=45, ha='right')
        ax.set_ylabel('重要性得分', fontsize=12, fontweight='bold')
        ax.set_title(f'{response_var} 的环境驱动因子', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2, height + 0.001,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=9)
        
        output_path = f"{self.output_dir}/{response_var}_驱动因子_Python版.png"
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ {response_var} 分析图已保存")
    
    def export_results(self):
        """导出结果"""
        print("\n💾 === 导出结果 ===")
        
        # 导出Excel报告
        with pd.ExcelWriter(f"{self.output_dir}/RDA分析结果_Python版.xlsx") as writer:
            
            # 基本统计
            basic_stats = pd.DataFrame({
                '项目': ['分析方法', '样本数量', '响应变量数', '环境变量数', '总解释度(%)'],
                '值': ['RDA (Python-CCA)', self.results['sample_size'], 
                      len(self.response_vars), len(self.X.columns), 
                      round(self.explained_variance, 2)]
            })
            basic_stats.to_excel(writer, sheet_name='基本统计', index=False)
            
            # 变量重要性
            overall_importance = self.create_variable_importance()
            overall_importance.to_excel(writer, sheet_name='变量重要性', index=False)
            
            # 分响应变量结果
            for var, results in self.individual_results.items():
                results['importance'].to_excel(writer, sheet_name=f'{var}_重要性', index=False)
        
        print(f"✅ Excel报告已保存: {self.output_dir}/RDA分析结果_Python版.xlsx")
        
        # 导出文本摘要
        with open(f"{self.output_dir}/分析摘要_Python版.txt", 'w', encoding='utf-8') as f:
            f.write("=== Python版RDA分析摘要 ===\n\n")
            f.write(f"分析方法: 冗余分析 (RDA) - 基于Python CCA\n")
            f.write(f"样本数量: {self.results['sample_size']}\n")
            f.write(f"响应变量数: {len(self.response_vars)}\n")
            f.write(f"环境变量数: {len(self.X.columns)}\n")
            f.write(f"总解释度: {self.explained_variance:.2f}%\n\n")
            
            f.write("响应变量:\n")
            for var in self.response_vars:
                f.write(f"  - {var}\n")
            
            f.write(f"\n前10个重要环境变量:\n")
            top_vars = overall_importance.tail(10)
            for _, row in top_vars.iterrows():
                f.write(f"  - {row['Variable']}: {row['Importance']:.4f}\n")
        
        print(f"✅ 文本摘要已保存: {self.output_dir}/分析摘要_Python版.txt")
        return self

def main():
    """主函数"""
    print("🐍 === Python版RDA冗余分析 ===")
    
    # 文件路径
    vif_file =r"E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_VIF筛选结果.xlsx"
    change_file = r"E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"
    
    # 执行分析
    rda = RDAAnalysis("E:/05 Python/Devway/01 硕士论文/07 Python_RDA分析")
    
    try:
        results = (rda
                  .load_data(vif_file, change_file)
                  .preprocess_data()
                  .perform_rda()
                  .create_biplot()
                  .individual_response_analysis()
                  .export_results())
        
        print("\n🎉 === Python版RDA分析完成 ===")
        print(f"📁 所有结果已保存至: {rda.output_dir}")
        
        return rda
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
