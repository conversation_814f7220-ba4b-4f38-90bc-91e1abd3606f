# =============================================================================
# 清理后代码测试脚本
# =============================================================================

# 清理环境
rm(list = ls())

# 加载必要的包
library(vegan)
library(readxl)
library(dplyr)

# 设置路径
vif_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_VIF_筛选变化量结果/分组VIF_筛选结果.xlsx"
change_file_path <- "E:/05 Python/Devway/01 硕士论文/00 原始数据/嫩江_模型预测数据/嫩江_变化量.xlsx"

# 响应变量定义
response_vars <- c("△pH", "△SOM", "△TN", "△TP", "△物理粘粒")

# 加载数据处理函数
source("06 RDA冗余分析/03_data_processing.R")

# 读取数据
cat("=== 读取数据 ===\n")
vif_data <- read_excel(vif_file_path, sheet = "筛选后数据")
change_data <- read_excel(change_file_path)

cat("VIF数据维度:", nrow(vif_data), "×", ncol(vif_data), "\n")
cat("变化量数据维度:", nrow(change_data), "×", ncol(change_data), "\n")

# 测试数据预处理
cat("\n=== 测试数据预处理 ===\n")
processed_result <- preprocess_data_with_transitions(
  vif_data = vif_data,
  change_data = change_data,
  response_vars = response_vars,
  create_transitions = TRUE
)

# 检查结果
cat("\n=== 检查处理结果 ===\n")
cat("处理后数据维度:", nrow(processed_result$data), "×", ncol(processed_result$data), "\n")
cat("协变量矩阵维度:", nrow(processed_result$explanatory_matrix), "×", ncol(processed_result$explanatory_matrix), "\n")
cat("响应变量数:", length(processed_result$response_vars), "\n")
cat("环境变量数:", length(processed_result$env_vars), "\n")

# 显示转移变量
cat("\n=== 转移变量列表 ===\n")
transition_vars <- grep("^(LU_|ST_|.*_Changed|Any_Change)", colnames(processed_result$explanatory_matrix), value = TRUE)
cat("转移变量数:", length(transition_vars), "\n")
for(var in transition_vars) {
  cat("  ", var, "\n")
}

# 准备RDA分析数据
cat("\n=== 准备RDA分析 ===\n")
response_matrix <- processed_result$data[, response_vars, drop = FALSE]
response_matrix <- response_matrix[complete.cases(response_matrix), ]

explanatory_matrix <- processed_result$explanatory_matrix
explanatory_matrix <- explanatory_matrix[rownames(response_matrix), ]

cat("最终分析数据维度:\n")
cat("  响应变量矩阵:", nrow(response_matrix), "×", ncol(response_matrix), "\n")
cat("  协变量矩阵:", nrow(explanatory_matrix), "×", ncol(explanatory_matrix), "\n")

# 执行RDA分析
cat("\n=== 执行RDA分析 ===\n")
if(nrow(response_matrix) > 0 && ncol(explanatory_matrix) > 0) {
  
  # 处理协变量缺失值
  for(i in 1:ncol(explanatory_matrix)) {
    if(is.numeric(explanatory_matrix[, i])) {
      explanatory_matrix[is.na(explanatory_matrix[, i]), i] <- mean(explanatory_matrix[, i], na.rm = TRUE)
    }
  }
  
  # 执行RDA
  rda_result <- rda(response_matrix ~ ., data = explanatory_matrix)
  
  # 显示结果摘要
  cat("RDA分析完成！\n")
  cat("总解释度:", round(RsquareAdj(rda_result)$adj.r.squared * 100, 2), "%\n")
  
  # 显著性检验
  overall_test <- anova(rda_result, permutations = 999)
  cat("整体显著性P值:", round(overall_test$`Pr(>F)`[1], 4), "\n")
  
  # 各轴解释度
  if(length(rda_result$CCA$eig) > 0) {
    axis_var <- rda_result$CCA$eig / sum(rda_result$CCA$eig) * RsquareAdj(rda_result)$adj.r.squared * 100
    cat("RDA1解释度:", round(axis_var[1], 2), "%\n")
    if(length(axis_var) > 1) {
      cat("RDA2解释度:", round(axis_var[2], 2), "%\n")
    }
  }
  
  cat("\n✅ 清理后的代码运行成功！\n")
  
} else {
  cat("❌ 数据准备失败，无法执行RDA分析\n")
}

cat("\n=== 测试完成 ===\n")
cat("核心功能验证:\n")
cat("✅ 数据读取和预处理\n")
cat("✅ 转移变量创建\n")
cat("✅ 协变量矩阵构建\n")
cat("✅ RDA分析执行\n")
cat("✅ 结果输出\n")

cat("\n代码已清理完成，保留核心功能，删除冗余部分！\n")
